<template>
  <div class="tianditu-map-container">
    <div 
      ref="mapContainer" 
      :id="mapId" 
      class="tianditu-map"
      :style="{ width: width, height: height }"
    ></div>
    
    <!-- 搜索框 -->
    <div v-if="props.showSearch" class="map-search-container">
      <el-autocomplete
        v-model="searchKeyword"
        :fetch-suggestions="querySearchAsync"
        placeholder="搜索地点"
        @select="handleSelect"
        @keyup.enter="handleSearch"
        @clear="handleSearchClear"
        clearable
        class="map-search-input"
        :loading="searchLoading"
        popper-class="map-search-popper"
      >
        <template #default="{ item }">
          <div class="search-item">
            <div class="search-item-name">{{ item.name }}</div>
            <div class="search-item-address">{{ item.address }}</div>
          </div>
        </template>
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-autocomplete>
    </div>
    
    <!-- 图层管理抽屉 -->
    <div v-if="props.enableLayers" class="layer-drawer" :class="{ 'layer-drawer-open': layerDrawerOpen }">
      <div class="layer-drawer-toggle" @click="toggleLayerDrawer">
        <el-icon>
          <svg viewBox="0 0 1024 1024" width="1em" height="1em">
            <path d="M128 256h768v64H128zM128 448h768v64H128zM128 640h768v64H128z" fill="currentColor"/>
          </svg>
        </el-icon>
        <span>图层</span>
      </div>
      <div class="layer-drawer-content">
        <div class="layer-drawer-header">
          <h3>图层管理</h3>
          <div class="layer-header-actions">
            <el-button
              @click="showAddLayerDialog = true"
              type="primary"
              size="small"
              plain
            >
              <el-icon><Plus /></el-icon>
              地图图层
            </el-button>
            <el-button
              @click="showAddMarkerLayerDialog = true"
              type="success"
              size="small"
              plain
            >
              <el-icon><Plus /></el-icon>
              标记图层
            </el-button>
          </div>
        </div>
        
        <!-- 标记图层管理 -->
        <div class="marker-layer-section">
          <h4 class="section-title">标记图层</h4>
          <div class="marker-layer-list">
            <div
              v-for="layer in markerLayers"
              :key="layer.id"
              class="marker-layer-item"
              :class="{ 'active': currentMarkerLayerId === layer.id }"
            >
              <div class="marker-layer-header">
                <div class="marker-layer-info">
                  <el-checkbox
                    v-model="layer.visible"
                    @change="toggleMarkerLayerVisibility(layer.id)"
                  >
                    <span class="marker-layer-name">{{ layer.name }}</span>
                  </el-checkbox>
                  <div v-if="editingLayerName !== layer.id" class="marker-layer-name-display">
                    <el-button 
                      @click="startEditLayerName(layer.id)" 
                      type="text" 
                      size="small" 
                      class="layer-edit-btn"
                      title="编辑图层名称"
                    >
                      <el-icon><Edit /></el-icon>
                    </el-button>
                  </div>
                  <div v-else class="marker-layer-name-edit">
                    <el-input 
                      v-model="editingLayerNameValue" 
                      size="small" 
                      @keyup.enter="saveLayerName" 
                      @blur="saveLayerName"
                      ref="layerNameInput"
                      placeholder="请输入图层名称"
                      class="layer-name-input"
                    />
                    <el-button 
                      @click="saveLayerName" 
                      type="primary" 
                      size="small" 
                      plain
                    >
                      <el-icon><Check /></el-icon>
                    </el-button>
                    <el-button 
                      @click="cancelEditLayerName" 
                      type="default" 
                      size="small" 
                      plain
                    >
                      <el-icon><Close /></el-icon>
                    </el-button>
                  </div>
                  <div class="marker-layer-color" :style="{ backgroundColor: layer.color }"></div>
                </div>
                <div class="marker-layer-actions">
                  <el-button
                    @click="setCurrentMarkerLayer(layer.id)"
                    :type="currentMarkerLayerId === layer.id ? 'primary' : 'default'"
                    size="small"
                    plain
                    title="设为当前图层"
                  >
                    <el-icon><Select /></el-icon>
                  </el-button>
                  <el-button
                    @click="removeMarkerLayer(layer.id)"
                    type="danger"
                    size="small"
                    plain
                    :disabled="layer.isDeletable === false"
                    :title="layer.isDeletable === false ? '默认图层不可删除' : '删除图层'"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>
              <div class="marker-layer-stats">
                <span class="stat-item">标点: {{ layer.markers.length }}</span>
                <span class="stat-item">标线: {{ layer.polylines.length }}</span>
                <span class="stat-item">{{ layer.symbolType }}</span>
              </div>
              <div v-if="layer.description" class="marker-layer-description">
                {{ layer.description }}
              </div>
            </div>
            <div v-if="markerLayers.length === 0" class="empty-marker-layers">
              <p>暂无标记图层</p>
              <el-button
                @click="showAddMarkerLayerDialog = true"
                type="primary"
                size="small"
                plain
              >
                <el-icon><Plus /></el-icon>
                创建第一个标记图层
              </el-button>
            </div>
          </div>
        </div>
        
        <!-- 地图图层管理 -->
        <div class="map-layer-section">
          <h4 class="section-title">地图图层</h4>
          <div class="layer-list">
          <div
            v-for="layer in customLayers"
            :key="layer.id"
            class="layer-item"
          >
            <div class="layer-item-header">
              <el-checkbox
                v-model="layer.visible"
                @change="toggleLayerVisibility(layer)"
              >
                {{ layer.name }}
              </el-checkbox>
              <div class="layer-item-actions">
                <el-button
                  @click="editLayer(layer)"
                  type="text"
                  size="small"
                  title="编辑图层"
                >
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button
                  @click="removeLayer(layer.id)"
                  type="text"
                  size="small"
                  title="删除图层"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
            <div class="layer-item-info">
              <div class="layer-item-type">{{ getLayerTypeText(layer.type) }}</div>
              <div class="layer-item-opacity">
                <span>透明度:</span>
                <el-slider
                  v-model="layer.opacity"
                  @change="updateLayerOpacity(layer)"
                  :min="0"
                  :max="1"
                  :step="0.1"
                  size="small"
                  style="width: 80px; margin-left: 8px;"
                />
              </div>
            </div>
          </div>
        </div>
        </div>
      </div>
    </div>

    <!-- 标记控制按钮 -->
    <div v-if="props.enableMarker" class="map-marker-controls">
      <el-button
        :type="markerMode ? 'primary' : 'default'"
        @click="toggleMarkerMode"
        class="marker-toggle-btn"
        circle
        :title="markerMode ? '退出标点模式' : '开启标点模式'"
      >
        <el-icon>
          <LocationFilled v-if="markerMode" />
          <Location v-else />
        </el-icon>
      </el-button>
      <el-button
        :type="polylineMode ? 'primary' : 'default'"
        @click="togglePolylineMode"
        class="polyline-toggle-btn"
        circle
        :title="polylineMode ? '退出标线模式' : '开启标线模式'"
      >
        <el-icon>
          <svg viewBox="0 0 1024 1024" width="1em" height="1em">
            <path d="M128 256h768v64H128zM128 448h768v64H128zM128 640h768v64H128z" fill="currentColor"/>
          </svg>
        </el-icon>
      </el-button>
      <el-button
        type="danger"
        @click="clearAllMarkers(true)"
        class="marker-clear-btn"
        circle
        title="清除所有标记"
      >
        <el-icon>
          <Delete />
        </el-icon>
      </el-button>
    </div>
    
    <!-- 轨迹动画控制面板 -->
    <div v-if="props.enableTrackAnimation" class="track-animation-controls">
      <div class="track-controls-toggle" @click="toggleTrackControls">
        <el-icon>
          <svg viewBox="0 0 1024 1024" width="1em" height="1em">
            <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm144.1 454.9L437.7 677.8a8 8 0 01-12.6-6.5V353.7a8 8 0 0112.6-6.5l218.4 158.9a7.9 7.9 0 010 12.8z" fill="currentColor"/>
          </svg>
        </el-icon>
        <span>轨迹</span>
      </div>
      <div class="track-controls-panel" :class="{ 'track-controls-open': trackControlsOpen }">
        <div class="track-controls-header">
          <h4>轨迹动画控制</h4>
        </div>
        <div class="track-controls-content">
          <div class="track-control-buttons">
            <el-button @click="playCurrentAnimation" :disabled="!currentTrackController" size="small">播放</el-button>
            <el-button @click="pauseCurrentAnimation" :disabled="!currentTrackController" size="small">暂停</el-button>
            <el-button @click="stopCurrentAnimation" :disabled="!currentTrackController" size="small">停止</el-button>
            <el-button @click="clearAllTrackAnimations" type="danger" size="small">清除</el-button>
          </div>
          
          <div class="track-speed-control">
            <label>速度倍数:</label>
            <el-select
              v-model="trackAnimationSpeedMultiplier"
              @change="updateTrackSpeed"
              size="small"
              style="width: 80px;"
              :disabled="currentTrackController && ((typeof currentTrackController.isPlaying === 'function' && currentTrackController.isPlaying()) || (typeof currentTrackController.isPaused === 'function' && currentTrackController.isPaused()))"
            >
              <el-option label="×1" :value="1" />
              <el-option label="×2" :value="2" />
              <el-option label="×4" :value="4" />
              <el-option label="×8" :value="8" />
              <el-option label="×12" :value="12" />
            </el-select>
            <span class="speed-tip" v-if="currentTrackController && ((typeof currentTrackController.isPlaying === 'function' && currentTrackController.isPlaying()) || (typeof currentTrackController.isPaused === 'function' && currentTrackController.isPaused()))">
              (需停止后调节)
            </span>
          </div>
          
          <div class="track-animation-info" v-if="currentTrackController">
            <div class="current-track-info">
              <p><strong>当前选中:</strong> {{ getCurrentTrackTitle() }}</p>
            </div>
            <div class="progress-info">
              <div class="progress-text">
                轨迹进度: {{ currentTrackProgress.currentIndex }} / {{ currentTrackProgress.totalPoints }}
                ({{ getTrackProgressPercentage() }}%)
              </div>
              <el-progress
                :percentage="getTrackProgressPercentage()"
                :stroke-width="6"
                :show-text="false"
                class="track-progress-bar"
              />
            </div>
            <p>状态: {{ getTrackAnimationStatus() }}</p>
            <p>动画数量: {{ carTrackAnimations.length }}</p>
          </div>

          <div class="track-selection-tip" v-else>
            <p>点击地图上的轨迹线来选择要控制的轨迹</p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 标点信息卡片 -->
    <div 
      v-if="currentMarkerInfo && showMarkerInfo" 
      class="marker-info-card"
      :style="markerInfoPosition"
    >
      <div class="marker-info-header">
        <div v-if="!editingMarkerTitle" class="marker-info-title-display">
          <h4 class="marker-info-title">{{ currentMarkerInfo.title }}</h4>
          <el-button 
            @click="startEditTitle" 
            type="text" 
            size="small" 
            class="marker-edit-btn"
            title="编辑标题"
          >
            <el-icon><Edit /></el-icon>
          </el-button>
        </div>
        <div v-else class="marker-info-title-edit">
          <el-input 
            v-model="editingTitleValue" 
            size="small" 
            @keyup.enter="saveTitle" 
            @blur="saveTitle"
            ref="titleInput"
            placeholder="请输入标点名称"
          />
          <el-button 
            @click="saveTitle" 
            type="primary" 
            size="small" 
            plain
          >
            <el-icon><Check /></el-icon>
          </el-button>
          <el-button 
            @click="cancelEditTitle" 
            type="default" 
            size="small" 
            plain
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
        <el-button 
          @click="closeMarkerInfo" 
          type="text" 
          size="small" 
          class="marker-info-close"
        >
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
      <div class="marker-info-content">
        <div v-if="currentMarkerInfo.locationName" class="marker-info-item">
          <span class="marker-info-label">地点:</span>
          <span class="marker-info-value">{{ currentMarkerInfo.locationName }}</span>
        </div>
        <div class="marker-info-item">
          <span class="marker-info-label">坐标:</span>
          <span class="marker-info-value">{{ currentMarkerInfo.content }}</span>
        </div>
        <div class="marker-info-item">
          <span class="marker-info-label">创建时间:</span>
          <span class="marker-info-value">{{ currentMarkerInfo.createTime }}</span>
        </div>
      </div>
      <div class="marker-info-actions">
        <el-button 
          @click="removeMarker(currentMarkerInfo.id)" 
          type="danger" 
          size="small"
          plain
        >
          <el-icon><Delete /></el-icon>
          删除
        </el-button>
      </div>
    </div>
    
    <!-- 标线信息卡片 -->
    <div 
      v-if="currentPolylineInfo && showPolylineInfo" 
      class="polyline-info-card"
      :style="polylineInfoPosition"
    >
      <div class="marker-info-header polyline-info-header">
        <div v-if="!editingPolylineTitle" class="marker-info-title-display">
          <h4 class="marker-info-title">{{ currentPolylineInfo.title }}</h4>
          <el-button 
            @click="startEditPolylineTitle" 
            type="text" 
            size="small" 
            class="marker-edit-btn"
            title="编辑标题"
          >
            <el-icon><Edit /></el-icon>
          </el-button>
        </div>
        <div v-else class="marker-info-title-edit">
          <el-input 
            v-model="editingPolylineTitleValue" 
            size="small" 
            @keyup.enter="savePolylineTitle" 
            @blur="savePolylineTitle"
            ref="polylineTitleInput"
            placeholder="请输入标线名称"
            class="polyline-title-input"
          />
          <el-button 
            @click="savePolylineTitle" 
            type="primary" 
            size="small" 
            plain
          >
            <el-icon><Check /></el-icon>
          </el-button>
          <el-button 
            @click="cancelEditPolylineTitle" 
            type="default" 
            size="small" 
            plain
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
        <el-button 
          @click="closePolylineInfo" 
          type="text" 
          size="small" 
          class="marker-info-close"
        >
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
      <div class="marker-info-content">
        <div class="marker-info-item">
          <span class="marker-info-label">总长度:</span>
          <span class="marker-info-value">{{ currentPolylineInfo.totalLength }}</span>
        </div>
        <div class="marker-info-item polyline-style-controls">
          <div class="polyline-style-row">
            <span class="marker-info-label">线条样式:</span>
            <el-select 
              v-model="currentPolylineInfo.lineStyle" 
              @change="updatePolylineStyle"
              size="small"
              class="polyline-style-select"
            >
              <el-option label="实线" value="solid" />
              <el-option label="虚线" value="dashed" />
            </el-select>
          </div>
          <div class="polyline-style-row">
            <span class="marker-info-label">颜色:</span>
            <el-color-picker 
              v-model="currentPolylineInfo.color" 
              @change="updatePolylineStyle"
              size="small"
              class="polyline-color-picker"
            />
          </div>
          <div class="polyline-style-row">
            <span class="marker-info-label">线宽:</span>
            <el-input-number 
              v-model="currentPolylineInfo.weight" 
              @change="updatePolylineStyle"
              :min="1" 
              :max="20" 
              size="small"
              class="polyline-weight-input"
            />
          </div>
        </div>
        <div class="marker-info-item">
          <span class="marker-info-label">创建时间:</span>
          <span class="marker-info-value">{{ currentPolylineInfo.createTime }}</span>
        </div>
      </div>
      <div class="marker-info-actions">
        <el-button 
          @click="removePolyline(currentPolylineInfo.id)" 
          type="danger" 
          size="small"
          plain
        >
          <el-icon><Delete /></el-icon>
          删除
        </el-button>
      </div>
    </div>
    
    <!-- 添加图层对话框 -->
    <el-dialog
      v-model="showAddLayerDialog"
      title="添加图层"
      width="500px"
      :before-close="handleAddLayerDialogClose"
    >
      <el-form :model="newLayerForm" label-width="80px">
        <el-form-item label="图层名称">
          <el-input v-model="newLayerForm.name" placeholder="请输入图层名称" />
        </el-form-item>
        <el-form-item label="图层类型">
          <el-select v-model="newLayerForm.type" placeholder="请选择图层类型">
            <el-option label="瓦片图层" value="tile" />
            <el-option label="图片覆盖" value="image" />
            <el-option label="WMS服务" value="wms" />
          </el-select>
        </el-form-item>
        <el-form-item label="服务地址" v-if="newLayerForm.type === 'tile' || newLayerForm.type === 'wms'">
          <el-input v-model="newLayerForm.url" placeholder="请输入服务地址" />
        </el-form-item>
        <el-form-item label="图片地址" v-if="newLayerForm.type === 'image'">
          <el-input v-model="newLayerForm.imageUrl" placeholder="请输入图片地址" />
        </el-form-item>
        <el-form-item label="边界范围" v-if="newLayerForm.type === 'image'">
          <el-row :gutter="10">
            <el-col :span="6">
              <el-input v-model="newLayerForm.bounds.minLng" placeholder="最小经度" type="number" />
            </el-col>
            <el-col :span="6">
              <el-input v-model="newLayerForm.bounds.minLat" placeholder="最小纬度" type="number" />
            </el-col>
            <el-col :span="6">
              <el-input v-model="newLayerForm.bounds.maxLng" placeholder="最大经度" type="number" />
            </el-col>
            <el-col :span="6">
              <el-input v-model="newLayerForm.bounds.maxLat" placeholder="最大纬度" type="number" />
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="透明度">
          <el-slider v-model="newLayerForm.opacity" :min="0" :max="1" :step="0.1" />
        </el-form-item>
        <el-form-item label="层级范围">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-input-number v-model="newLayerForm.minZoom" :min="1" :max="18" placeholder="最小层级" />
            </el-col>
            <el-col :span="12">
              <el-input-number v-model="newLayerForm.maxZoom" :min="1" :max="18" placeholder="最大层级" />
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddLayerDialog = false">取消</el-button>
          <el-button type="primary" @click="addLayer">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加标记图层对话框 -->
    <el-dialog
      v-model="showAddMarkerLayerDialog"
      title="添加标记图层"
      width="500px"
      :before-close="handleAddMarkerLayerDialogClose"
    >
      <el-form :model="newMarkerLayerForm" label-width="100px">
        <el-form-item label="图层名称" required>
          <el-input v-model="newMarkerLayerForm.name" placeholder="请输入图层名称" />
        </el-form-item>
        <el-form-item label="图层颜色">
          <el-color-picker v-model="newMarkerLayerForm.color" />
        </el-form-item>
        <el-form-item label="符号类型">
          <el-select v-model="newMarkerLayerForm.symbolType" placeholder="请选择符号类型">
            <el-option label="圆形" value="circle" />
            <el-option label="方形" value="square" />
            <el-option label="三角形" value="triangle" />
            <el-option label="星形" value="star" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>
        <el-form-item label="自定义图标" v-if="newMarkerLayerForm.symbolType === 'custom'">
          <el-input v-model="newMarkerLayerForm.icon" placeholder="请输入图标URL" />
        </el-form-item>
        <el-form-item label="图层描述">
          <el-input
            v-model="newMarkerLayerForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入图层描述（可选）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddMarkerLayerDialog = false">取消</el-button>
          <el-button type="primary" @click="addMarkerLayer">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <div v-if="loading" class="map-loading">
      <el-icon class="is-loading">
        <Loading />
      </el-icon>
      <span>地图加载中...</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { ElIcon, ElAutocomplete, ElMessage, ElButton, ElInput, ElSelect, ElOption, ElColorPicker, ElInputNumber, ElDialog, ElForm, ElFormItem, ElCheckbox, ElRow, ElCol, ElSlider, ElProgress } from 'element-plus'
import { Loading, Search, Location, LocationFilled, Delete, Close, Edit, Check, Plus, Select } from '@element-plus/icons-vue'
import { buildShortUUID } from '@/utils/uuid'

// 天地图和D3.js类型声明
declare global {
  interface Window {
    T: any
    d3: any
  }
}

// 定义组件属性
interface Props {
  width?: string  // 地图宽度
  height?: string // 地图高度
  center?: [number, number] // 地图中心点坐标[经度, 纬度]
  zoom?: number // 地图缩放级别
  minZoom?: number  // 地图最小缩放级别
  maxZoom?: number  // 地图最大缩放级别
  mapType?: 'vec' | 'img' | 'ter' // 地图类型：矢量、影像、地形
  showControls?: boolean // 是否显示地图控件
  showScale?: boolean // 是否显示比例尺
  showOverview?: boolean // 是否显示鹰眼图
  enableScrollWheelZoom?: boolean // 启用滚轮缩放
  showSearch?: boolean // 显示搜索框
  enableMarker?: boolean // 启用右侧标记栏功能
  markerIcon?: string // 自定义标点图标URL
  enableLayers?: boolean // 启用图层管理功能
  enableTrackAnimation?: boolean // 启用轨迹动画功能
}

// 搜索结果接口
interface SearchResult {
  name: string // 地点名称
  address: string // 详细地址
  lonlat: string // 经纬度
  hotPointID: string // 热点ID
}

// 标记图层接口
interface MarkerLayerData {
  id: string // 图层ID
  name: string // 图层名称
  type: 'marker' // 图层类型，固定为marker
  visible: boolean // 是否可见
  color: string // 图层颜色
  icon?: string // 图层图标URL
  symbolType?: 'circle' | 'square' | 'triangle' | 'star' | 'custom' // 符号类型
  description?: string // 图层描述
  markers: MarkerData[] // 该图层下的标点
  polylines: PolylineData[] // 该图层下的标线
  createTime?: string // 创建时间
  isDeletable?: boolean // 是否可删除
}

// 标点接口
interface MarkerData {
  id: string // 标点ID
  lng: number // 经度
  lat: number // 纬度
  title?: string // 标点标题
  content?: string // 标点内容
  createTime?: string // 创建时间
  locationName?: string // 实际地点名称
  layerId?: string // 所属图层ID
  marker?: any // 天地图标记实例
}

// 标线接口
interface PolylineData {
  id: string // 标线ID
  points: Array<{lng: number, lat: number}> // 标线点集合
  title: string // 标线标题
  totalLength: string // 总长度（格式化字符串）
  color: string // 线条颜色
  weight: number // 线条宽度
  lineStyle: string // 线条样式
  createTime: string // 创建时间
  layerId?: string // 所属图层ID
  polyline?: any // 天地图标线实例
}

// 图层接口
interface LayerData {
  id: string // 图层ID
  name: string // 图层名称
  type: 'tile' | 'image' | 'wms' // 图层类型
  url?: string // 服务地址
  imageUrl?: string // 图片地址
  bounds?: {
    minLng: number
    minLat: number
    maxLng: number
    maxLat: number
  } // 边界范围
  opacity: number // 透明度
  visible: boolean // 是否可见
  minZoom?: number // 最小层级
  maxZoom?: number // 最大层级
  layerObject?: any // 天地图图层实例
}

const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '500px',
  center: () => [116.397428, 39.90923], // 默认北京天安门
  zoom: 12,
  minZoom: 1,
  maxZoom: 18,
  mapType: 'vec',
  showControls: true,
  showScale: true,
  showOverview: false,
  enableScrollWheelZoom: true,
  showSearch: true,
  enableMarker: true,
  enableLayers: false,
  markerIcon: '',
  enableTrackAnimation: false
})

// 定义事件
const emit = defineEmits<{
  mapReady: [map: any]
  mapClick: [event: any]
  searchResult: [result: SearchResult]
  markerAdd: [marker: MarkerData]
  markerRemove: [markerId: string]
  markerClick: [marker: MarkerData]
  polylineAdd: [polyline: PolylineData]
  polylineRemove: [polylineId: string]
  polylineClick: [polyline: PolylineData]
  trackAnimationCreate: [animation: CarTrackData]
  trackAnimationRemove: [animationId: string]
  trackAnimationStart: [animationId: string]
  trackAnimationPause: [animationId: string]
  trackAnimationStop: [animationId: string]
  trackAnimationComplete: [animationId: string]
}>()

// 响应式数据
const mapContainer = ref<HTMLElement>()
const loading = ref(true)
const mapId = ref(`tianditu-map-${buildShortUUID()}`)
const map = ref<any>(null)

// 搜索相关数据
const searchKeyword = ref('')
const searchLoading = ref(false)
const searchMarkers = ref<any[]>([]) // 存储搜索结果标记

// 标点相关数据
const markerMode = ref(false) // 标点模式开关
const customMarkers = ref<MarkerData[]>([]) // 存储用户添加的标记
const markerCounter = ref(0) // 标记计数器

// 信息卡片相关数据
const currentMarkerInfo = ref<MarkerData | null>(null) // 当前显示的标记信息
const showMarkerInfo = ref(false) // 是否显示信息卡片
const markerInfoPosition = ref({ left: '0px', top: '0px' }) // 信息卡片位置
const editingMarkerTitle = ref(false) // 是否正在编辑标题
const editingTitleValue = ref('') // 编辑中的标题值
const titleInput = ref<any>(null) // 标题输入框引用
const editingLayerName = ref<string | null>(null) // 正在编辑名称的图层ID
const editingLayerNameValue = ref('') // 编辑中的图层名称值
const layerNameInput = ref<any>(null) // 图层名称输入框引用

// 标线相关数据
const polylineMode = ref(false) // 标线模式开关
const customPolylines = ref<PolylineData[]>([]) // 存储用户添加的标线
const polylineCounter = ref(0) // 标线计数器
const currentDrawingPoints = ref<Array<{lng: number, lat: number}>>([]) // 当前绘制中的点集合
const currentDrawingPolyline = ref<any>(null) // 当前绘制中的临时标线
const drawingMarkers = ref<any[]>([]) // 绘制过程中的临时标记点

// 标线信息卡片相关数据
const currentPolylineInfo = ref<PolylineData | null>(null) // 当前显示的标线信息
const showPolylineInfo = ref(false) // 是否显示标线信息卡片
const polylineInfoPosition = ref({ left: '0px', top: '0px' }) // 标线信息卡片位置
const editingPolylineTitle = ref(false) // 是否正在编辑标线标题
const editingPolylineTitleValue = ref('') // 编辑中的标线标题值
const polylineTitleInput = ref<any>(null) // 标线标题输入框引用

// 轨迹动画相关数据（使用官方CarTrack）
const carTrackAnimations = ref<CarTrackData[]>([]) // 存储轨迹动画数据
const carTrackControllers = ref<Map<string, CarTrackController>>(new Map()) // 动画控制器映射
const trackAnimationCounter = ref(0) // 轨迹动画计数器
const trackControlsOpen = ref(false) // 轨迹控制面板是否打开
const currentTrackController = ref<CarTrackController | null>(null) // 当前轨迹动画控制器
const trackAnimationSpeedMultiplier = ref(1) // 轨迹动画速度倍数，默认1倍
// 轨迹进度状态（基于官方passOneNode回调）
const currentTrackProgress = ref({ currentIndex: 0, totalPoints: 0 })

// 图层管理相关数据
const layerDrawerOpen = ref(false) // 图层抽屉是否打开
const customLayers = ref<LayerData[]>([]) // 自定义地图图层列表
const markerLayers = ref<MarkerLayerData[]>([{
  id: 'default-layer',
  name: '默认图层',
  type: 'marker',
  visible: true,
  color: '#409EFF',
  symbolType: 'circle',
  icon: '',
  description: '系统默认标记图层',
  markers: [],
  polylines: [],
  createTime: new Date().toLocaleString(),
  isDeletable: false
}]) // 标记图层列表
const showAddLayerDialog = ref(false) // 是否显示添加地图图层对话框
const showAddMarkerLayerDialog = ref(false) // 是否显示添加标记图层对话框
const layerCounter = ref(0) // 地图图层计数器
const markerLayerCounter = ref(0) // 标记图层计数器
const currentMarkerLayerId = ref<string>('default-layer') // 当前选中的标记图层ID
const newLayerForm = ref({
  name: '',
  type: 'tile' as 'tile' | 'image' | 'wms',
  url: '',
  imageUrl: '',
  bounds: {
    minLng: 0,
    minLat: 0,
    maxLng: 0,
    maxLat: 0
  },
  opacity: 1,
  minZoom: 1,
  maxZoom: 18
}) // 新地图图层表单数据

const newMarkerLayerForm = ref({
  name: '',
  color: '#409EFF',
  icon: '',
  symbolType: 'circle' as 'circle' | 'square' | 'triangle' | 'star' | 'custom',
  description: ''
}) // 新标记图层表单数据

// 天地图API密钥
const API_KEY = import.meta.env.VITE_TDT_API_KEY

// 加载脚本的通用方法
const loadScript = (src: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    // 检查是否已经加载过该脚本
    const existingScript = document.querySelector(`script[src="${src}"]`)
    if (existingScript) {
      resolve()
      return
    }

    const script = document.createElement('script')
    script.type = 'text/javascript'
    script.src = src
    script.onload = () => resolve()
    script.onerror = () => reject(new Error(`脚本加载失败: ${src}`))
    document.head.appendChild(script)
  })
}

// 加载天地图基础API
const loadTiandituAPI = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    // 检查是否已经加载
    if (window.T) {
      resolve()
      return
    }

    // 创建script标签加载天地图API
    const script = document.createElement('script')
    script.type = 'text/javascript'
    script.src = `https://api.tianditu.gov.cn/api?v=4.0&tk=${API_KEY}`
    script.onload = () => {
      if (window.T) {
        resolve()
      } else {
        reject(new Error('天地图API加载失败'))
      }
    }
    script.onerror = () => {
      reject(new Error('天地图API加载失败'))
    }
    document.head.appendChild(script)
  })
}

// 加载轨迹动画相关依赖
const loadTrackAnimationDependencies = async (): Promise<void> => {
  try {
    const win = window as any

    // 确保基础API已加载
    if (!win.T) {
      throw new Error('天地图基础API未加载')
    }

    // 加载D3.js库（CarTrack的依赖）
    if (!win.d3) {
      await loadScript('http://lbs.tianditu.gov.cn/js/lib/d3/d3.min.js')

      if (!win.d3) {
        throw new Error('D3.js库加载失败')
      }
    }

    // 加载天地图D3支持库
    if (!win.T.D3Overlay) {
      await loadScript('http://lbs.tianditu.gov.cn/api/js4.0/opensource/openlibrary/D3SvgOverlay.js')

      if (!win.T.D3Overlay) {
        throw new Error('天地图D3支持库加载失败')
      }
    }

    // 加载CarTrack类
    if (!win.T.CarTrack) {
      await loadScript('http://lbs.tianditu.gov.cn/api/js4.0/opensource/openlibrary/CarTrack.js')

      if (!win.T.CarTrack) {
        throw new Error('CarTrack类加载失败')
      }
    }

    console.log('轨迹动画依赖加载完成')

  } catch (error) {
    console.error('轨迹动画依赖加载失败:', error)
    throw error
  }
}

// 初始化地图
const initMap = async () => {
  try {
    loading.value = true
    
    // 加载天地图API
    await loadTiandituAPI()
    
    // 等待DOM更新
    await nextTick()

    // 创建地图实例
    map.value = new window.T.Map(mapId.value)
    
    // 设置地图中心点和缩放级别
    const centerPoint = new window.T.LngLat(props.center[0], props.center[1])
    map.value.centerAndZoom(centerPoint, props.zoom)
    
    // 设置缩放范围
    map.value.setMinZoom(props.minZoom)
    map.value.setMaxZoom(props.maxZoom)
    
    // 启用滚轮缩放
    if (props.enableScrollWheelZoom) {
      map.value.enableScrollWheelZoom()
    }
    
    // 添加地图类型控件
    if (props.showControls) {
      map.value.addControl(new window.T.Control.MapType())
      map.value.addControl(new window.T.Control.Zoom())
    }
    
    // 添加比例尺控件
    if (props.showScale) {
      map.value.addControl(new window.T.Control.Scale())
    }
    
    // 添加鹰眼控件
    if (props.showOverview) {
      const overviewMap = new window.T.Control.OverviewMap({
        isOpen: true, // 默认展开
        size: new window.T.Point(200, 150) // 设置鹰眼图大小
      })
      map.value.addControl(overviewMap)
    }
    
    // 设置地图类型
    setMapType(props.mapType)
    
    // 添加右键菜单
    addContextMenu()
    
    // 绑定基础事件
    bindMapEvents()

    // 如果启用了轨迹动画功能，预加载相关依赖
    if (props.enableTrackAnimation) {
      ensureTrackAnimationDependencies().then(success => {
        if (success) {
          console.log('轨迹动画依赖预加载完成')
        } else {
          console.warn('轨迹动画依赖预加载失败，使用时将重新尝试加载')
        }
      }).catch(error => {
        console.warn('轨迹动画依赖预加载出错:', error)
      })
    }

    loading.value = false

    // 触发地图就绪事件
    emit('mapReady', map.value)
    
  } catch (error) {
    console.error('地图初始化失败:', error)
    loading.value = false
  }
}

// 设置地图类型
const setMapType = (type: string) => {
  if (!map.value) return
  
  // 使用天地图4.0 API的setMapType方法
  switch (type) {
    case 'img':
      // 影像地图
      map.value.setMapType(TMAP_SATELLITE_MAP)
      break
    case 'ter':
      // 地形地图 (使用卫星混合作为地形的替代)
      map.value.setMapType(TMAP_HYBRID_MAP)
      break
    default:
      // 矢量地图
      map.value.setMapType(TMAP_NORMAL_MAP)
  }
}

// 添加右键菜单
const addContextMenu = () => {
  if (!map.value) return
  
  try {
    // 创建右键菜单
    const menu = new window.T.ContextMenu({
      width: 120
    })
    
    // 定义菜单项
    const menuItems = [
      {
        text: '放大',
        callback: () => {
          map.value.zoomIn()
        }
      },
      {
        text: '缩小',
        callback: () => {
          map.value.zoomOut()
        }
      },
      {
        text: '放置到最大级',
        callback: () => {
          map.value.setZoom(18)
        }
      },
      {
        text: '查看全国',
        callback: () => {
          map.value.setZoom(4)
        }
      }
    ]
    
    // 添加菜单项
    for (let i = 0; i < menuItems.length; i++) {
      const menuItem = new window.T.MenuItem(menuItems[i].text, menuItems[i].callback)
      menu.addItem(menuItem)
      
      // 在缩小后添加分割线
      if (i === 1) {
        menu.addSeparator()
      }
    }
    
    // 将右键菜单添加到地图
    map.value.addContextMenu(menu)
    
    console.log('右键菜单已添加')
  } catch (error) {
    console.error('添加右键菜单失败:', error)
  }
}

// 绑定地图事件
const bindMapEvents = () => {
  if (!map.value) return
  
  // 地图点击事件
  map.value.addEventListener('click', (event: any) => {
    // 如果开启了标点模式，则在点击位置添加标记
    if (markerMode.value && props.enableMarker) {
      addMarkerAtPosition(event.lnglat.lng, event.lnglat.lat)
    }
    // 如果开启了标线模式，则添加标线点
    else if (polylineMode.value && props.enableMarker) {
      addPolylinePoint(event.lnglat.lng, event.lnglat.lat)
    }
    emit('mapClick', event)
  })
  
  // 地图双击事件（完成标线绘制）
  map.value.addEventListener('dblclick', (event: any) => {
    if (polylineMode.value && currentDrawingPoints.value.length >= 2) {
      finishPolylineDrawing()
    }
  })
}

// 搜索建议词（根据官方文档增强）
const searchSuggestions = async (keyword: string): Promise<SearchResult[]> => {
  if (!keyword.trim()) return []
  
  return new Promise((resolve) => {
    try {
      // 使用天地图JavaScript SDK的建议词搜索功能
      const localSearch = new window.T.LocalSearch(map.value, {
        pageCapacity: 10,
        onSearchComplete: (result: any) => {
          try {
            console.log('天地图建议词搜索结果:', result)

            // 解析建议词信息
            const suggests = result.getSuggests()
            console.log('建议词数据:', suggests)

            if (suggests && suggests.length > 0) {
              const suggestResults: SearchResult[] = suggests.map((suggest: any) => ({
                name: suggest.name || '',
                address: suggest.address || '',
                lonlat: suggest.lonlat || '',
                hotPointID: suggest.hotPointID || ''
              }))
              console.log('解析后的建议词结果:', suggestResults)
              resolve(suggestResults)
            } else {
              console.log('没有找到建议词数据')
              resolve([])
            }
          } catch (error) {
            console.error('解析建议词结果失败:', error)
            resolve([])
          }
        }
      })

      // 执行建议词搜索（搜索类型为4表示建议词搜索）
      localSearch.search(keyword, 4)
    } catch (error) {
      console.error('建议词搜索失败:', error)
      resolve([])
    }
  })
}

// 搜索地点
const searchPlace = async (keyword: string): Promise<SearchResult[]> => {
  if (!keyword.trim()) return []
  
  return new Promise((resolve) => {
    try {
      // 使用天地图JavaScript SDK的地名搜索功能
      const localSearch = new window.T.LocalSearch(map.value, {
        pageCapacity: 10,
        onSearchComplete: (result: any) => {
          try {
            console.log('天地图搜索结果:', result)

            // 根据返回类型解析搜索结果
            const resultType = result.getResultType()
            console.log('搜索结果类型:', resultType)

            switch (parseInt(resultType)) {
              case 1:
                // 解析点数据结果（POI数据）
                const pois = result.getPois()
                console.log('POI数据:', pois)

                if (pois && pois.length > 0) {
                  console.log('第一个POI结构:', pois[0])

                  const searchResults: SearchResult[] = pois.map((poi: any) => ({
                    name: poi.name || '',
                    address: poi.address || '',
                    lonlat: poi.lonlat || '',
                    hotPointID: poi.hotPointID || ''
                  }))
                  console.log('解析后的搜索结果:', searchResults)
                  resolve(searchResults)
                } else {
                  console.log('没有找到POI数据')
                  resolve([])
                }
                break

              case 2:
                // 解析推荐城市
                const statistics = result.getStatistics()
                console.log('推荐城市数据:', statistics)

                if (statistics && statistics.length > 0) {
                  const cityResults: SearchResult[] = statistics.map((city: any) => ({
                    name: city.name || '',
                    address: city.name || '',
                    lonlat: city.lonlat || '',
                    hotPointID: city.adminCode || ''
                  }))
                  resolve(cityResults)
                } else {
                  resolve([])
                }
                break

              case 3:
                // 解析行政区划边界
                const area = result.getArea()
                console.log('行政区划数据:', area)

                if (area) {
                  const areaResult: SearchResult = {
                    name: area.name || '',
                    address: area.name || '',
                    lonlat: area.lonlat || '',
                    hotPointID: area.adminCode || ''
                  }
                  resolve([areaResult])
                } else {
                  resolve([])
                }
                break

              case 4:
                // 解析建议词
                const suggests = result.getSuggests()
                console.log('建议词数据:', suggests)

                if (suggests && suggests.length > 0) {
                  const suggestResults: SearchResult[] = suggests.map((suggest: any) => ({
                    name: suggest.name || '',
                    address: suggest.address || '',
                    lonlat: suggest.lonlat || '',
                    hotPointID: suggest.hotPointID || ''
                  }))
                  resolve(suggestResults)
                } else {
                  resolve([])
                }
                break

              case 5:
                // 解析线数据结果
                const lineData = result.getLineData()
                console.log('线数据:', lineData)

                if (lineData && lineData.length > 0) {
                  const lineResults: SearchResult[] = lineData.map((line: any) => ({
                    name: line.name || '',
                    address: line.name || '',
                    lonlat: line.lonlat || '',
                    hotPointID: line.lineID || ''
                  }))
                  resolve(lineResults)
                } else {
                  resolve([])
                }
                break

              default:
                console.warn('未知的搜索结果类型:', resultType)
                resolve([])
                break
            }
          } catch (error) {
            console.error('解析搜索结果失败:', error)
            resolve([])
          }
        }
      })

      // 执行搜索
      localSearch.search(keyword)
    } catch (error) {
      console.error('搜索地点失败:', error)
      ElMessage.error('搜索功能初始化失败')
      resolve([])
    }
  })
}

// 异步搜索建议（使用建议词搜索增强）
const querySearchAsync = async (queryString: string, callback: (results: SearchResult[]) => void) => {
  if (!queryString.trim()) {
    callback([])
    return
  }
  
  searchLoading.value = true
  try {
    // 优先使用建议词搜索获取更准确的搜索建议
    const suggestions = await searchSuggestions(queryString)
    
    if (suggestions.length > 0) {
      // 如果建议词搜索有结果，直接返回
      callback(suggestions)
    } else {
      // 如果建议词搜索无结果，回退到普通搜索
      const results = await searchPlace(queryString)
      callback(results)
    }
  } finally {
    searchLoading.value = false
  }
}

// 处理搜索选择
const handleSelect = (item: SearchResult) => {
  if (!item.lonlat) return
  
  // 天地图返回的lonlat格式可能是 "lng,lat" 或 "lng lat"
  const coords = item.lonlat.includes(',') ? item.lonlat.split(',') : item.lonlat.split(' ')
  const [lng, lat] = coords.map(Number)
  
  // 清除之前的搜索标记
  clearSearchMarkers()
  
  // 添加标记
  const marker = new window.T.Marker(new window.T.LngLat(lng, lat))
  map.value.addOverLay(marker)
  searchMarkers.value.push(marker)
  
  // 移动地图到该位置
  panTo(lng, lat)
  
  // 保留搜索文本
  searchKeyword.value = item.name
  
  // 触发搜索结果事件
  emit('searchResult', item)
  
  ElMessage.success(`已定位到：${item.name}`)
}

// 处理搜索回车（使用建议词搜索增强）
const handleSearch = async () => {
  if (!searchKeyword.value.trim()) return
  
  searchLoading.value = true
  try {
    // 优先使用建议词搜索
    let results = await searchSuggestions(searchKeyword.value)
    
    // 如果建议词搜索无结果，使用普通搜索
    if (results.length === 0) {
      results = await searchPlace(searchKeyword.value)
    }
    
    if (results.length > 0) {
      handleSelect(results[0]) // 选择第一个结果
    } else {
      ElMessage.warning('未找到相关地点')
    }
  } finally {
    searchLoading.value = false
  }
}

// 监听搜索框清除事件
const handleSearchClear = () => {
  clearSearchMarkers()
}

// 清除搜索标记
const clearSearchMarkers = () => {
  searchMarkers.value.forEach(marker => {
    map.value.removeOverLay(marker)
  })
  searchMarkers.value = []
}

// 切换标点模式
const toggleMarkerMode = () => {
  // 如果当前是标线模式，先关闭标线模式
  if (polylineMode.value) {
    polylineMode.value = false
    // 取消当前绘制
    cancelCurrentDrawing()
  }
  
  markerMode.value = !markerMode.value
  
  // 更新地图鼠标样式
  if (markerMode.value) {
    // 开启标点模式时，改变鼠标样式为十字
    if (map.value && map.value.getContainer) {
      const container = map.value.getContainer()
      if (container) {
        container.style.cursor = 'crosshair'
      }
    }
    ElMessage.success('标点模式已开启，点击地图添加标记')
  } else {
    // 关闭标点模式时，恢复默认鼠标样式
    if (map.value && map.value.getContainer) {
      const container = map.value.getContainer()
      if (container) {
        container.style.cursor = 'default'
      }
    }
    ElMessage.info('标点模式已关闭')
  }
}

// 获取地点名称（逆地理编码）
const getLocationName = (lng: number, lat: number): Promise<string> => {
  return new Promise((resolve) => {
    try {
      // 创建逆地理编码对象
      const geocoder = new window.T.Geocoder()
      const lngLat = new window.T.LngLat(lng, lat)
      
      geocoder.getLocation(lngLat, (result: any) => {
        try {
          if (result.getStatus() === 0) {
            // 获取详细地址
            const address = result.getAddress()
            if (address) {
              resolve(address)
              return
            }
            
            // 如果没有详细地址，尝试获取组件信息
            const addressComponent = result.getAddressComponent()
            if (addressComponent) {
              // 优先使用POI点名称
              if (addressComponent.poi) {
                resolve(addressComponent.poi)
                return
              }
              // 其次使用最近地点信息
              if (addressComponent.address) {
                resolve(addressComponent.address)
                return
              }
              // 最后使用城市信息
              if (addressComponent.city) {
                resolve(addressComponent.city)
                return
              }
            }
          }
          resolve('未知地点')
        } catch (error) {
          console.warn('解析地理编码结果失败:', error)
          resolve('未知地点')
        }
      })
    } catch (error) {
      console.warn('逆地理编码失败:', error)
      resolve('未知地点')
    }
  })
}

// 在指定位置添加标记
const addMarkerAtPosition = async (lng: number, lat: number, title?: string, content?: string, layerId?: string) => {
  if (!map.value) return null
  
  // 如果没有指定图层ID，使用当前选中的标记图层
  const targetLayerId = layerId || currentMarkerLayerId.value
  
  const finalLayerId = targetLayerId || currentMarkerLayerId.value
  const targetLayer = markerLayers.value.find(layer => layer.id === finalLayerId)
  
  if (!targetLayer) {
    ElMessage.warning('当前图层不存在，已切换到默认图层')
    currentMarkerLayerId.value = 'default-layer'
    return null
  }
  
  markerCounter.value++
  const markerId = `marker_${markerCounter.value}_${Date.now()}`
  
  // 创建标记位置
  const position = new window.T.LngLat(lng, lat)
  
  // 创建自定义图标（根据图层配置）
  let marker
  const iconUrl = targetLayer.icon || props.markerIcon
  
  if (iconUrl) {
    const icon = new window.T.Icon({
      iconUrl: iconUrl,
      iconSize: new window.T.Point(32, 32),
      iconAnchor: new window.T.Point(16, 32)
    })
    marker = new window.T.Marker(position, { icon })
  } else {
    // 使用默认标记，根据图层颜色和符号类型自定义样式
    marker = new window.T.Marker(position)
    
    // 如果有图层颜色，可以通过CSS类名来设置样式
    if (targetLayer.color && targetLayer.color !== '#409EFF') {
      // 这里可以添加自定义样式逻辑
      marker.setIcon(createCustomIcon(targetLayer.color, targetLayer.symbolType))
    }
  }
  
  // 只有在图层可见时才添加到地图
  if (targetLayer.visible) {
    map.value.addOverLay(marker)
  }
  
  // 获取地点名称
  const locationName = await getLocationName(lng, lat)
  
  // 创建标记数据
  const markerData: MarkerData = {
    id: markerId,
    lng,
    lat,
    title: title || `标记 ${markerCounter.value}`,
    content: content || `经度: ${lng.toFixed(6)}, 纬度: ${lat.toFixed(6)}`,
    createTime: new Date().toLocaleString('zh-CN'),
    locationName,
    layerId: finalLayerId,
    marker
  }
  
  // 添加点击事件
  marker.addEventListener('click', (event: any) => {
    emit('markerClick', markerData)
    showMarkerInfoCard(markerData)
  })
  
  // 存储标记数据到全局数组和对应图层
  customMarkers.value.push(markerData)
  targetLayer.markers.push(markerData)
  
  // 触发添加事件
  emit('markerAdd', markerData)
  
  // 添加标记后立即显示信息卡片
  nextTick(() => {
    showMarkerInfoCard(markerData)
  })
  
  return markerData
}

// 创建自定义图标
const createCustomIcon = (color: string, symbolType: string) => {
  // 创建SVG图标
  const size = 32
  const halfSize = size / 2
  
  let svgContent = ''
  
  switch (symbolType) {
    case 'circle':
      svgContent = `<circle cx="${halfSize}" cy="${halfSize}" r="${halfSize - 2}" fill="${color}" stroke="white" stroke-width="2"/>`
      break
    case 'square':
      svgContent = `<rect x="2" y="2" width="${size - 4}" height="${size - 4}" fill="${color}" stroke="white" stroke-width="2"/>`
      break
    case 'triangle':
      svgContent = `<polygon points="${halfSize},2 ${size - 2},${size - 2} 2,${size - 2}" fill="${color}" stroke="white" stroke-width="2"/>`
      break
    case 'star':
      const points = []
      for (let i = 0; i < 10; i++) {
        const angle = (i * Math.PI) / 5
        const radius = i % 2 === 0 ? halfSize - 2 : (halfSize - 2) / 2
        const x = halfSize + radius * Math.cos(angle - Math.PI / 2)
        const y = halfSize + radius * Math.sin(angle - Math.PI / 2)
        points.push(`${x},${y}`)
      }
      svgContent = `<polygon points="${points.join(' ')}" fill="${color}" stroke="white" stroke-width="1"/>`
      break
    default:
      svgContent = `<circle cx="${halfSize}" cy="${halfSize}" r="${halfSize - 2}" fill="${color}" stroke="white" stroke-width="2"/>`
  }
  
  const svg = `<svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">${svgContent}</svg>`
  const iconUrl = 'data:image/svg+xml;base64,' + btoa(svg)
  
  return new window.T.Icon({
    iconUrl: iconUrl,
    iconSize: new window.T.Point(size, size),
    iconAnchor: new window.T.Point(halfSize, size)
  })
}

// 显示标记信息卡片
const showMarkerInfoCard = (markerData: MarkerData, event?: any) => {
  currentMarkerInfo.value = markerData
  showMarkerInfo.value = true
  
  // 计算信息卡片位置 - 智能定位算法
  const containerRect = mapContainer.value?.getBoundingClientRect()
  if (containerRect && map.value && markerData.lng && markerData.lat) {
    try {
      // 将地理坐标转换为屏幕像素坐标
      const lngLat = new window.T.LngLat(markerData.lng, markerData.lat)
      const pixel = map.value.lngLatToContainerPoint(lngLat)
      
      // 信息卡片尺寸
      const cardWidth = 300
      const cardHeight = 200
      const offset = 50 // 与标记点的距离
      const padding = 15 // 距离容器边缘的最小距离
      
      // 计算四个方向的可用空间
      const spaceTop = pixel.y - padding
      const spaceBottom = containerRect.height - pixel.y - padding
      const spaceLeft = pixel.x - padding
      const spaceRight = containerRect.width - pixel.x - padding
      
      let position = { left: 0, top: 0 }
      
      // 智能选择最佳位置（优先级：右侧 > 左侧 > 上方 > 下方）
      if (spaceRight >= cardWidth + offset) {
        // 右侧有足够空间
        position.left = pixel.x + offset
        position.top = Math.max(padding, Math.min(
          pixel.y - cardHeight / 2,
          containerRect.height - cardHeight - padding
        ))
      } else if (spaceLeft >= cardWidth + offset) {
        // 左侧有足够空间
        position.left = pixel.x - cardWidth - offset
        position.top = Math.max(padding, Math.min(
          pixel.y - cardHeight / 2,
          containerRect.height - cardHeight - padding
        ))
      } else if (spaceTop >= cardHeight + offset) {
        // 上方有足够空间
        position.left = Math.max(padding, Math.min(
          pixel.x - cardWidth / 2,
          containerRect.width - cardWidth - padding
        ))
        position.top = pixel.y - cardHeight - offset
      } else if (spaceBottom >= cardHeight + offset) {
        // 下方有足够空间
        position.left = Math.max(padding, Math.min(
          pixel.x - cardWidth / 2,
          containerRect.width - cardWidth - padding
        ))
        position.top = pixel.y + offset
      } else {
        // 空间不足，选择最大可用空间的方向
        const maxSpace = Math.max(spaceTop, spaceBottom, spaceLeft, spaceRight)
        
        if (maxSpace === spaceRight) {
          position.left = Math.min(pixel.x + offset, containerRect.width - cardWidth - padding)
          position.top = Math.max(padding, Math.min(
            pixel.y - cardHeight / 2,
            containerRect.height - cardHeight - padding
          ))
        } else if (maxSpace === spaceLeft) {
          position.left = Math.max(padding, pixel.x - cardWidth - offset)
          position.top = Math.max(padding, Math.min(
            pixel.y - cardHeight / 2,
            containerRect.height - cardHeight - padding
          ))
        } else if (maxSpace === spaceTop) {
          position.left = Math.max(padding, Math.min(
            pixel.x - cardWidth / 2,
            containerRect.width - cardWidth - padding
          ))
          position.top = Math.max(padding, pixel.y - cardHeight - offset)
        } else {
          position.left = Math.max(padding, Math.min(
            pixel.x - cardWidth / 2,
            containerRect.width - cardWidth - padding
          ))
          position.top = Math.min(pixel.y + offset, containerRect.height - cardHeight - padding)
        }
      }
      
      markerInfoPosition.value = {
        left: `${position.left}px`,
        top: `${position.top}px`
      }
      
      console.log('标点信息卡片位置:', position, '标记点像素坐标:', pixel)
    } catch (error) {
      console.warn('计算标记点屏幕坐标失败:', error)
      // 降级到默认位置
      markerInfoPosition.value = {
        left: '50%',
        top: '50%',
        transform: 'translate(-50%, -50%)'
      }
    }
  } else {
    // 默认位置
    markerInfoPosition.value = {
      left: '50%',
      top: '50%',
      transform: 'translate(-50%, -50%)'
    }
  }
}

// 关闭标记信息卡片
const closeMarkerInfo = () => {
  showMarkerInfo.value = false
  currentMarkerInfo.value = null
  editingMarkerTitle.value = false
  editingTitleValue.value = ''
}

// 开始编辑标题
const startEditTitle = () => {
  if (currentMarkerInfo.value) {
    editingMarkerTitle.value = true
    editingTitleValue.value = currentMarkerInfo.value.title || ''
    nextTick(() => {
      try {
        if (titleInput.value) {
          if (typeof titleInput.value.focus === 'function') {
            titleInput.value.focus()
          } else if (titleInput.value.input && typeof titleInput.value.input.focus === 'function') {
            titleInput.value.input.focus()
          } else if (titleInput.value.$el && titleInput.value.$el.querySelector) {
            const inputEl = titleInput.value.$el.querySelector('input')
            if (inputEl && typeof inputEl.focus === 'function') {
              inputEl.focus()
            }
          }
        }
      } catch (error) {
        console.warn('无法聚焦到标题输入框:', error)
      }
    })
  }
}

// 保存标题
const saveTitle = () => {
  if (currentMarkerInfo.value && editingTitleValue.value.trim()) {
    const markerId = currentMarkerInfo.value.id
    const markerIndex = customMarkers.value.findIndex(m => m.id === markerId)
    
    if (markerIndex !== -1) {
      // 更新标记数据
      customMarkers.value[markerIndex].title = editingTitleValue.value.trim()
      currentMarkerInfo.value.title = editingTitleValue.value.trim()
      
      ElMessage.success('标点名称已更新')
    }
  }
  
  editingMarkerTitle.value = false
  editingTitleValue.value = ''
}

// 取消编辑标题
const cancelEditTitle = () => {
  editingMarkerTitle.value = false
  editingTitleValue.value = ''
}

// 开始编辑图层名称
const startEditLayerName = (layerId: string) => {
  const layer = markerLayers.value.find(l => l.id === layerId)
  if (layer) {
    editingLayerName.value = layerId
    editingLayerNameValue.value = layer.name
    nextTick(() => {
      try {
        if (layerNameInput.value) {
          if (typeof layerNameInput.value.focus === 'function') {
            layerNameInput.value.focus()
          } else if (layerNameInput.value.input && typeof layerNameInput.value.input.focus === 'function') {
            layerNameInput.value.input.focus()
          } else if (layerNameInput.value.$el && layerNameInput.value.$el.querySelector) {
            const inputEl = layerNameInput.value.$el.querySelector('input')
            if (inputEl && typeof inputEl.focus === 'function') {
              inputEl.focus()
            }
          }
        }
      } catch (error) {
        console.warn('无法聚焦到图层名称输入框:', error)
      }
    })
  }
}

// 保存图层名称
const saveLayerName = () => {
  if (editingLayerName.value && editingLayerNameValue.value.trim()) {
    const layer = markerLayers.value.find(l => l.id === editingLayerName.value)
    if (layer) {
      layer.name = editingLayerNameValue.value.trim()
      ElMessage.success('图层名称已更新')
    }
  }
  
  editingLayerName.value = null
  editingLayerNameValue.value = ''
}

// 取消编辑图层名称
const cancelEditLayerName = () => {
  editingLayerName.value = null
  editingLayerNameValue.value = ''
}

// 删除指定标记
const removeMarker = (markerId: string) => {
  const markerIndex = customMarkers.value.findIndex(m => m.id === markerId)
  if (markerIndex === -1) return false
  
  const markerData = customMarkers.value[markerIndex]
  
  // 从地图移除标记
  if (markerData.marker && map.value) {
    map.value.removeOverLay(markerData.marker)
  }
  
  // 从数组中移除
  customMarkers.value.splice(markerIndex, 1)
  
  // 关闭信息卡片（如果当前显示的是被删除的标记）
  if (currentMarkerInfo.value?.id === markerId) {
    closeMarkerInfo()
  }
  
  // 触发删除事件
  emit('markerRemove', markerId)
  
  ElMessage.success('标记已删除')
  return true
}

// 清除所有自定义标记
const clearAllMarkers = (showMessage: boolean = false) => {
  // 清除旧的自定义标记（保持向后兼容）
  customMarkers.value.forEach(markerData => {
    if (markerData.marker && map.value) {
      map.value.removeOverLay(markerData.marker)
    }
  })
  
  customMarkers.value = []
  markerCounter.value = 0
  
  // 清除可见图层中的标记和标线
  let clearedMarkersCount = 0
  let clearedPolylinesCount = 0
  
  markerLayers.value.forEach(layer => {
    if (layer.visible) {
      // 清除该图层的标记
      layer.markers.forEach(marker => {
        if (marker.marker && map.value) {
          map.value.removeOverLay(marker.marker)
          clearedMarkersCount++
        }
      })
      layer.markers = []
      
      // 清除该图层的标线
      layer.polylines.forEach(polyline => {
        if (polyline.polyline && map.value) {
          map.value.removeOverLay(polyline.polyline)
          clearedPolylinesCount++
        }
      })
      layer.polylines = []
    }
  })
  
  // 关闭信息卡片
  closeMarkerInfo()
  
  // 只有在用户主动点击清除按钮时才显示提示消息
  if (showMessage) {
    if (clearedMarkersCount > 0 || clearedPolylinesCount > 0) {
      ElMessage.success(`已清除 ${clearedMarkersCount} 个标记和 ${clearedPolylinesCount} 条标线`)
    } else {
      ElMessage.info('当前可见图层中没有标记或标线可清除')
    }
  } else {
    console.log(`已清除 ${clearedMarkersCount} 个标记和 ${clearedPolylinesCount} 条标线`)
  }
}

// ==================== 标线功能 ====================

// 切换标线模式
const togglePolylineMode = () => {
  // 如果当前是标点模式，先关闭标点模式
  if (markerMode.value) {
    markerMode.value = false
  }
  
  polylineMode.value = !polylineMode.value
  
  if (polylineMode.value) {
    // 开启标线模式
    if (map.value && map.value.getContainer) {
      const container = map.value.getContainer()
      if (container) {
        container.style.cursor = 'crosshair'
      }
    }
    ElMessage.success('标线模式已开启，点击地图添加标线点，双击或按Enter键完成绘制')
  } else {
    // 关闭标线模式，清理当前绘制状态
    cancelCurrentDrawing()
    if (map.value && map.value.getContainer) {
      const container = map.value.getContainer()
      if (container) {
        container.style.cursor = 'default'
      }
    }
    ElMessage.info('标线模式已关闭')
  }
}

// 添加标线点
const addPolylinePoint = (lng: number, lat: number) => {
  if (!map.value) return
  
  // 添加点到当前绘制集合
  currentDrawingPoints.value.push({ lng, lat })
  
  // 创建临时标记点
  const marker = new window.T.Marker(new window.T.LngLat(lng, lat))
  map.value.addOverLay(marker)
  drawingMarkers.value.push(marker)
  
  // 如果有两个或以上的点，创建/更新临时标线
  if (currentDrawingPoints.value.length >= 2) {
    updateCurrentDrawingPolyline()
  }
  
  console.log(`已添加标线点: ${lng}, ${lat}，当前点数: ${currentDrawingPoints.value.length}`)
}

// 更新当前绘制中的标线
const updateCurrentDrawingPolyline = () => {
  if (!map.value || currentDrawingPoints.value.length < 2) return
  
  // 移除之前的临时标线
  if (currentDrawingPolyline.value) {
    map.value.removeOverLay(currentDrawingPolyline.value)
  }
  
  // 创建新的临时标线
  const points = currentDrawingPoints.value.map(p => new window.T.LngLat(p.lng, p.lat))
  currentDrawingPolyline.value = new window.T.Polyline(points, {
    color: '#ff0000',
    weight: 3,
    opacity: 0.8,
    lineStyle: 'solid'
  })
  
  map.value.addOverLay(currentDrawingPolyline.value)
}

// 完成标线绘制
const finishPolylineDrawing = () => {
  if (!map.value || currentDrawingPoints.value.length < 2) {
    ElMessage.warning('至少需要两个点才能创建标线')
    return
  }
  
  // 获取当前选中的图层
  const currentLayer = markerLayers.value.find(layer => layer.id === currentMarkerLayerId.value)
  if (!currentLayer) {
    ElMessage.warning('当前图层不存在，已切换到默认图层')
    currentMarkerLayerId.value = 'default-layer'
    return
  }
  
  polylineCounter.value++
  const polylineId = `polyline_${polylineCounter.value}_${Date.now()}`
  
  // 计算总长度
  const totalLength = calculatePolylineLength(currentDrawingPoints.value)
  
  // 创建标线数据
  const polylineData: PolylineData = {
    id: polylineId,
    points: [...currentDrawingPoints.value],
    title: `标线 ${polylineCounter.value}`,
    totalLength: formatDistance(totalLength),
    color: currentLayer.color || '#ff0000',
    weight: 3,
    lineStyle: 'solid',
    createTime: new Date().toLocaleString('zh-CN'),
    layerId: currentLayer.id
  }
  
  // 移除临时绘制元素
  cancelCurrentDrawing()
  
  // 创建正式标线
  const points = polylineData.points.map(p => new window.T.LngLat(p.lng, p.lat))
  const polyline = new window.T.Polyline(points, {
    color: polylineData.color,
    weight: polylineData.weight,
    opacity: 1,
    lineStyle: polylineData.lineStyle
  })
  
  // 添加点击事件
  polyline.addEventListener('click', (event: any) => {
    emit('polylineClick', polylineData)
    showPolylineInfoCard(polylineData, event)
  })
  
  // 只有在图层可见时才添加到地图
  if (currentLayer.visible) {
    map.value.addOverLay(polyline)
  }
  polylineData.polyline = polyline
  
  // 存储标线数据到全局数组和对应图层
  customPolylines.value.push(polylineData)
  currentLayer.polylines.push(polylineData)
  
  // 触发添加事件
  emit('polylineAdd', polylineData)
  
  // 显示信息卡片
  nextTick(() => {
    showPolylineInfoCard(polylineData)
  })
  
  // 自动关闭标线模式
  polylineMode.value = false
  
  // 重置光标样式
  if (map.value && map.value.getContainer) {
    const container = map.value.getContainer()
    if (container) {
      container.style.cursor = 'default'
    }
  }
  
  ElMessage.success(`标线创建成功，总长度: ${polylineData.totalLength}`)
}

// 取消当前绘制
const cancelCurrentDrawing = () => {
  // 清除临时标记点
  drawingMarkers.value.forEach(marker => {
    if (map.value) {
      map.value.removeOverLay(marker)
    }
  })
  drawingMarkers.value = []
  
  // 清除临时标线
  if (currentDrawingPolyline.value && map.value) {
    map.value.removeOverLay(currentDrawingPolyline.value)
    currentDrawingPolyline.value = null
  }
  
  // 清空当前绘制点集合
  currentDrawingPoints.value = []
}

// 计算标线长度（米）
const calculatePolylineLength = (points: Array<{lng: number, lat: number}>): number => {
  let totalDistance = 0
  
  for (let i = 0; i < points.length - 1; i++) {
    const point1 = new window.T.LngLat(points[i].lng, points[i].lat)
    const point2 = new window.T.LngLat(points[i + 1].lng, points[i + 1].lat)
    totalDistance += point1.distanceTo(point2)
  }
  
  return totalDistance
}

// 格式化距离显示
const formatDistance = (distance: number): string => {
  if (distance < 1000) {
    return `${distance.toFixed(2)} 米`
  } else {
    return `${(distance / 1000).toFixed(2)} 公里`
  }
}

// 显示标线信息卡片
const showPolylineInfoCard = (polylineData: PolylineData, event?: any) => {
  // 检查 polylineData 是否为 null 或 undefined
  if (!polylineData || !polylineData.points || polylineData.points.length === 0) {
    console.error('showPolylineInfoCard: polylineData 无效', polylineData)
    return
  }
  
  currentPolylineInfo.value = polylineData
  showPolylineInfo.value = true
  
  // 计算信息卡片位置 - 使用标线中点位置
  const containerRect = mapContainer.value?.getBoundingClientRect()
  if (containerRect && map.value && polylineData.points.length > 0) {
    try {
      // 计算标线中点
      const midIndex = Math.floor(polylineData.points.length / 2)
      const midPoint = polylineData.points[midIndex]
      const lngLat = new window.T.LngLat(midPoint.lng, midPoint.lat)
      const pixel = map.value.lngLatToContainerPoint(lngLat)
      
      // 信息卡片尺寸
      const cardWidth = 350
      const cardHeight = 280
      const offset = 50
      const padding = 15
      
      // 计算四个方向的可用空间
      const spaceTop = pixel.y - padding
      const spaceBottom = containerRect.height - pixel.y - padding
      const spaceLeft = pixel.x - padding
      const spaceRight = containerRect.width - pixel.x - padding
      
      let position = { left: 0, top: 0 }
      
      // 智能选择最佳位置
      if (spaceRight >= cardWidth + offset) {
        position.left = pixel.x + offset
        position.top = Math.max(padding, Math.min(
          pixel.y - cardHeight / 2,
          containerRect.height - cardHeight - padding
        ))
      } else if (spaceLeft >= cardWidth + offset) {
        position.left = pixel.x - cardWidth - offset
        position.top = Math.max(padding, Math.min(
          pixel.y - cardHeight / 2,
          containerRect.height - cardHeight - padding
        ))
      } else if (spaceTop >= cardHeight + offset) {
        position.left = Math.max(padding, Math.min(
          pixel.x - cardWidth / 2,
          containerRect.width - cardWidth - padding
        ))
        position.top = pixel.y - cardHeight - offset
      } else {
        position.left = Math.max(padding, Math.min(
          pixel.x - cardWidth / 2,
          containerRect.width - cardWidth - padding
        ))
        position.top = pixel.y + offset
      }
      
      polylineInfoPosition.value = {
        left: `${position.left}px`,
        top: `${position.top}px`
      }
      
      console.log('标线信息卡片位置计算:', position)
    } catch (error) {
      console.error('计算标线信息卡片位置失败:', error)
      // 降级到默认位置
      polylineInfoPosition.value = { left: '50px', top: '50px' }
    }
  }
}

// 关闭标线信息卡片
const closePolylineInfo = () => {
  showPolylineInfo.value = false
  currentPolylineInfo.value = null
  editingPolylineTitle.value = false
  editingPolylineTitleValue.value = ''
}

// 开始编辑标线标题
const startEditPolylineTitle = () => {
  if (!currentPolylineInfo.value) return
  
  editingPolylineTitle.value = true
  editingPolylineTitleValue.value = currentPolylineInfo.value.title
  
  nextTick(() => {
    try {
      if (polylineTitleInput.value) {
        if (typeof polylineTitleInput.value.focus === 'function') {
          polylineTitleInput.value.focus()
        } else if (polylineTitleInput.value.input && typeof polylineTitleInput.value.input.focus === 'function') {
          polylineTitleInput.value.input.focus()
        } else if (polylineTitleInput.value.$el && polylineTitleInput.value.$el.querySelector) {
          const inputEl = polylineTitleInput.value.$el.querySelector('input')
          if (inputEl && typeof inputEl.focus === 'function') {
            inputEl.focus()
          }
        }
      }
    } catch (error) {
      console.warn('无法聚焦到标线标题输入框:', error)
    }
  })
}

// 保存标线标题
const savePolylineTitle = () => {
  if (!currentPolylineInfo.value || !editingPolylineTitleValue.value.trim()) {
    cancelEditPolylineTitle()
    return
  }
  
  // 更新标线数据
  currentPolylineInfo.value.title = editingPolylineTitleValue.value.trim()
  
  // 更新存储的标线数据
  const polylineIndex = customPolylines.value.findIndex(p => p.id === currentPolylineInfo.value!.id)
  if (polylineIndex !== -1) {
    customPolylines.value[polylineIndex].title = currentPolylineInfo.value.title
  }
  
  editingPolylineTitle.value = false
  editingPolylineTitleValue.value = ''
  
  ElMessage.success('标线标题已更新')
}

// 取消编辑标线标题
const cancelEditPolylineTitle = () => {
  editingPolylineTitle.value = false
  editingPolylineTitleValue.value = ''
}

// 更新标线样式
const updatePolylineStyle = () => {
  if (!currentPolylineInfo.value || !currentPolylineInfo.value.polyline) return
  
  try {
    // 移除旧标线
    map.value.removeOverLay(currentPolylineInfo.value.polyline)
    
    // 创建新标线
    const points = currentPolylineInfo.value.points.map(p => new window.T.LngLat(p.lng, p.lat))
    const newPolyline = new window.T.Polyline(points, {
      color: currentPolylineInfo.value.color,
      weight: currentPolylineInfo.value.weight,
      opacity: 1,
      lineStyle: currentPolylineInfo.value.lineStyle
    })
    
    // 添加点击事件
    const polylineId = currentPolylineInfo.value.id // 保存当前标线ID
    newPolyline.addEventListener('click', (event: any) => {
      // 从存储的标线数据中找到对应的标线
      const polylineData = customPolylines.value.find(p => p.id === polylineId)
      if (polylineData) {
        emit('polylineClick', polylineData)
        showPolylineInfoCard(polylineData, event)
      }
    })
    
    // 添加到地图
    map.value.addOverLay(newPolyline)
    
    // 更新引用
    currentPolylineInfo.value.polyline = newPolyline
    
    // 更新存储的标线数据
    const polylineIndex = customPolylines.value.findIndex(p => p.id === currentPolylineInfo.value!.id)
    if (polylineIndex !== -1) {
      customPolylines.value[polylineIndex] = { ...currentPolylineInfo.value }
    }
    
    console.log('标线样式已更新')
  } catch (error) {
    console.error('更新标线样式失败:', error)
    ElMessage.error('更新标线样式失败')
  }
}

// 删除指定标线
const removePolyline = (polylineId: string) => {
  const polylineIndex = customPolylines.value.findIndex(p => p.id === polylineId)
  if (polylineIndex === -1) return false
  
  const polylineData = customPolylines.value[polylineIndex]
  
  // 从地图移除标线
  if (polylineData.polyline && map.value) {
    map.value.removeOverLay(polylineData.polyline)
  }
  
  // 从数组中移除
  customPolylines.value.splice(polylineIndex, 1)
  
  // 关闭信息卡片（如果当前显示的是被删除的标线）
  if (currentPolylineInfo.value?.id === polylineId) {
    closePolylineInfo()
  }
  
  // 触发删除事件
  emit('polylineRemove', polylineId)
  
  ElMessage.success('标线已删除')
  return true
}

// 清除所有标线
const clearAllPolylines = (showMessage: boolean = false) => {
  customPolylines.value.forEach(polylineData => {
    if (polylineData.polyline && map.value) {
      map.value.removeOverLay(polylineData.polyline)
    }
  })
  
  customPolylines.value = []
  polylineCounter.value = 0
  
  // 取消当前绘制
  cancelCurrentDrawing()
  
  // 关闭信息卡片
  closePolylineInfo()
  
  if (showMessage) {
    ElMessage.success('所有标线已清除')
  } else {
    console.log('所有标线已清除')
  }
}

// 图层管理相关方法
const toggleLayerDrawer = () => {
  layerDrawerOpen.value = !layerDrawerOpen.value
}

// 标记图层管理方法
const addMarkerLayer = () => {
  if (!newMarkerLayerForm.value.name.trim()) {
    ElMessage.warning('请输入图层名称')
    return
  }
  
  markerLayerCounter.value++
  const layerId = `marker_layer_${markerLayerCounter.value}_${Date.now()}`
  
  const markerLayer: MarkerLayerData = {
    id: layerId,
    name: newMarkerLayerForm.value.name,
    type: 'marker',
    visible: true,
    color: newMarkerLayerForm.value.color,
    icon: newMarkerLayerForm.value.icon,
    symbolType: newMarkerLayerForm.value.symbolType,
    description: newMarkerLayerForm.value.description,
    markers: [],
    polylines: [],
    createTime: new Date().toLocaleString(),
    isDeletable: true
  }
  
  markerLayers.value.push(markerLayer)
  currentMarkerLayerId.value = layerId
  showAddMarkerLayerDialog.value = false
  resetNewMarkerLayerForm()
  
  ElMessage.success(`标记图层 "${markerLayer.name}" 创建成功`)
}

const removeMarkerLayer = (layerId: string) => {
  const layerIndex = markerLayers.value.findIndex(layer => layer.id === layerId)
  if (layerIndex === -1) return
  
  const layer = markerLayers.value[layerIndex]
  
  // 检查是否为不可删除的图层
  if (layer.isDeletable === false) {
    ElMessage.warning('默认图层不能删除')
    return
  }
  
  // 移除该图层下的所有标记和标线
  layer.markers.forEach(marker => {
    if (marker.marker && map.value) {
      map.value.removeOverLay(marker.marker)
    }
  })
  
  layer.polylines.forEach(polyline => {
    if (polyline.polyline && map.value) {
      map.value.removeOverLay(polyline.polyline)
    }
  })
  
  // 从全局数组中移除
  customMarkers.value = customMarkers.value.filter(marker => marker.layerId !== layerId)
  customPolylines.value = customPolylines.value.filter(polyline => polyline.layerId !== layerId)
  
  // 移除图层
  markerLayers.value.splice(layerIndex, 1)
  
  // 如果删除的是当前选中图层，切换到默认图层
  if (currentMarkerLayerId.value === layerId) {
    currentMarkerLayerId.value = 'default-layer'
  }
  
  ElMessage.success(`标记图层 "${layer.name}" 已删除`)
}

const toggleMarkerLayerVisibility = (layerId: string) => {
  const layer = markerLayers.value.find(l => l.id === layerId)
  if (!layer) return
  
  // 切换该图层下所有标记的可见性
  layer.markers.forEach(marker => {
    if (marker.marker && map.value) {
      if (layer.visible) {
        map.value.addOverLay(marker.marker)
      } else {
        map.value.removeOverLay(marker.marker)
      }
    }
  })
  
  // 切换该图层下所有标线的可见性
  layer.polylines.forEach(polyline => {
    if (polyline.polyline && map.value) {
      if (layer.visible) {
        map.value.addOverLay(polyline.polyline)
      } else {
        map.value.removeOverLay(polyline.polyline)
      }
    }
  })
}

const setCurrentMarkerLayer = (layerId: string) => {
  currentMarkerLayerId.value = layerId
}

const resetNewMarkerLayerForm = () => {
  newMarkerLayerForm.value = {
    name: '',
    color: '#409EFF',
    icon: '',
    symbolType: 'circle',
    description: ''
  }
}

const handleAddMarkerLayerDialogClose = () => {
  resetNewMarkerLayerForm()
}

const clearAllMarkerLayers = () => {
  markerLayers.value.forEach(layer => {
    removeMarkerLayer(layer.id)
  })
  markerLayers.value = []
  currentMarkerLayerId.value = ''
}

const getLayerTypeText = (type: string) => {
  const typeMap = {
    tile: '瓦片图层',
    image: '图片覆盖',
    wms: 'WMS服务'
  }
  return typeMap[type] || type
}

const addLayer = () => {
  if (!newLayerForm.value.name.trim()) {
    ElMessage.warning('请输入图层名称')
    return
  }
  
  if (newLayerForm.value.type === 'tile' || newLayerForm.value.type === 'wms') {
    if (!newLayerForm.value.url.trim()) {
      ElMessage.warning('请输入服务地址')
      return
    }
  }
  
  if (newLayerForm.value.type === 'image') {
    if (!newLayerForm.value.imageUrl.trim()) {
      ElMessage.warning('请输入图片地址')
      return
    }
    if (!newLayerForm.value.bounds.minLng || !newLayerForm.value.bounds.minLat || 
        !newLayerForm.value.bounds.maxLng || !newLayerForm.value.bounds.maxLat) {
      ElMessage.warning('请输入完整的边界范围')
      return
    }
  }
  
  const layerData: LayerData = {
    id: `layer_${++layerCounter.value}`,
    name: newLayerForm.value.name,
    type: newLayerForm.value.type,
    url: newLayerForm.value.url,
    imageUrl: newLayerForm.value.imageUrl,
    bounds: { ...newLayerForm.value.bounds },
    opacity: newLayerForm.value.opacity,
    visible: true,
    minZoom: newLayerForm.value.minZoom,
    maxZoom: newLayerForm.value.maxZoom,
    layerObject: null
  }
  
  try {
    createLayerObject(layerData)
    customLayers.value.push(layerData)
    showAddLayerDialog.value = false
    resetNewLayerForm()
    ElMessage.success('图层添加成功')
  } catch (error) {
    console.error('添加图层失败:', error)
    ElMessage.error('添加图层失败')
  }
}

const createLayerObject = (layerData: LayerData) => {
  if (!map.value) return
  
  let layerObject = null
  
  try {
    if (layerData.type === 'tile') {
      // 创建瓦片图层
      layerObject = new window.T.TileLayer(layerData.url, {
        minZoom: layerData.minZoom,
        maxZoom: layerData.maxZoom
      })
    } else if (layerData.type === 'image') {
      // 创建图片覆盖
      const bounds = new window.T.LngLatBounds(
        new window.T.LngLat(layerData.bounds!.minLng, layerData.bounds!.minLat),
        new window.T.LngLat(layerData.bounds!.maxLng, layerData.bounds!.maxLat)
      )
      layerObject = new window.T.ImageOverlay(layerData.imageUrl, bounds)
    } else if (layerData.type === 'wms') {
      // 创建WMS图层
      layerObject = new window.T.TileLayer.WMS(layerData.url, {
        minZoom: layerData.minZoom,
        maxZoom: layerData.maxZoom
      })
    }
    
    if (layerObject) {
      layerData.layerObject = layerObject
      if (layerData.visible) {
        map.value.addOverLay(layerObject)
      }
      
      // 设置透明度
      if (layerObject.setOpacity) {
        layerObject.setOpacity(layerData.opacity)
      }
    }
  } catch (error) {
    console.error('创建图层对象失败:', error)
    throw error
  }
}

const toggleLayerVisibility = (layer: LayerData) => {
  if (!map.value || !layer.layerObject) return
  
  try {
    if (layer.visible) {
      map.value.addOverLay(layer.layerObject)
    } else {
      map.value.removeOverLay(layer.layerObject)
    }
  } catch (error) {
    console.error('切换图层可见性失败:', error)
    ElMessage.error('切换图层可见性失败')
  }
}

const updateLayerOpacity = (layer: LayerData) => {
  if (!layer.layerObject || !layer.layerObject.setOpacity) return
  
  try {
    layer.layerObject.setOpacity(layer.opacity)
  } catch (error) {
    console.error('更新图层透明度失败:', error)
    ElMessage.error('更新图层透明度失败')
  }
}

const removeLayer = (layerId: string) => {
  const layerIndex = customLayers.value.findIndex(layer => layer.id === layerId)
  if (layerIndex === -1) return
  
  const layer = customLayers.value[layerIndex]
  
  try {
    if (layer.layerObject && map.value) {
      map.value.removeOverLay(layer.layerObject)
    }
    customLayers.value.splice(layerIndex, 1)
    ElMessage.success('图层删除成功')
  } catch (error) {
    console.error('删除图层失败:', error)
    ElMessage.error('删除图层失败')
  }
}

const editLayer = (layer: LayerData) => {
  // 填充表单数据
  newLayerForm.value = {
    name: layer.name,
    type: layer.type,
    url: layer.url || '',
    imageUrl: layer.imageUrl || '',
    bounds: layer.bounds ? { ...layer.bounds } : {
      minLng: 0,
      minLat: 0,
      maxLng: 0,
      maxLat: 0
    },
    opacity: layer.opacity,
    minZoom: layer.minZoom || 1,
    maxZoom: layer.maxZoom || 18
  }
  
  // 先删除原图层
  removeLayer(layer.id)
  
  // 显示编辑对话框
  showAddLayerDialog.value = true
}

const resetNewLayerForm = () => {
  newLayerForm.value = {
    name: '',
    type: 'tile',
    url: '',
    imageUrl: '',
    bounds: {
      minLng: 0,
      minLat: 0,
      maxLng: 0,
      maxLat: 0
    },
    opacity: 1,
    minZoom: 1,
    maxZoom: 18
  }
}

const handleAddLayerDialogClose = () => {
  resetNewLayerForm()
  showAddLayerDialog.value = false
}

const clearAllLayers = () => {
  customLayers.value.forEach(layer => {
    if (layer.layerObject && map.value) {
      try {
        map.value.removeOverLay(layer.layerObject)
      } catch (error) {
        console.error('移除图层失败:', error)
      }
    }
  })
  customLayers.value = []
  layerCounter.value = 0
  console.log('所有图层已清除')
}

// 暴露给父组件的方法
const getMap = () => map.value
const getCenter = () => map.value?.getCenter()
const getZoom = () => map.value?.getZoom()
const setCenter = (lng: number, lat: number) => {
  if (map.value) {
    const point = new window.T.LngLat(lng, lat)
    map.value.setCenter(point)
  }
}
const setZoom = (zoom: number) => {
  if (map.value) {
    map.value.setZoom(zoom)
  }
}
const panTo = (lng: number, lat: number) => {
  if (map.value) {
    const point = new window.T.LngLat(lng, lat)
    map.value.panTo(point)
  }
}

// 监听属性变化
watch(() => props.center, (newCenter) => {
  if (map.value) {
    setCenter(newCenter[0], newCenter[1])
  }
})

watch(() => props.zoom, (newZoom) => {
  if (map.value) {
    setZoom(newZoom)
  }
})

watch(() => props.mapType, (newType) => {
  if (map.value) {
    setMapType(newType)
  }
})

// 键盘事件监听
const handleKeyDown = (event: KeyboardEvent) => {
  // ESC键取消当前绘制
  if (event.key === 'Escape' && polylineMode.value) {
    cancelCurrentDrawing()
  }
  
  // Enter键完成标线绘制
  if (event.key === 'Enter' && polylineMode.value && !editingPolylineTitle.value && currentDrawingPoints.value.length >= 2) {
    finishPolylineDrawing()
    return
  }
  
  // Enter键保存标线标题编辑
  if (event.key === 'Enter' && editingPolylineTitle.value) {
    savePolylineTitle()
  }
  
  // ESC键取消标线标题编辑
  if (event.key === 'Escape' && editingPolylineTitle.value) {
    cancelEditPolylineTitle()
  }
}

// 标线相关方法
const getPolylines = () => customPolylines.value
const addPolyline = (points: Array<{lng: number, lat: number}>, options?: Partial<PolylineData>) => {
  if (!map.value || points.length < 2) return null
  
  // 如果没有指定图层ID，使用当前选中的标记图层
  const targetLayerId = options?.layerId || currentMarkerLayerId.value
  
  const finalLayerId = targetLayerId || currentMarkerLayerId.value
  const targetLayer = markerLayers.value.find(layer => layer.id === finalLayerId)
  
  if (!targetLayer) {
    ElMessage.warning('当前图层不存在，已切换到默认图层')
    currentMarkerLayerId.value = 'default-layer'
    return null
  }
  
  const polylineData: PolylineData = {
    id: `polyline_${++polylineCounter.value}`,
    points,
    title: options?.title || `标线 ${polylineCounter.value}`,
    totalLength: formatDistance(calculatePolylineLength(points)),
    color: options?.color || targetLayer.color || '#ff0000',
    weight: options?.weight || 3,
    lineStyle: options?.lineStyle || 'solid',
    createTime: new Date().toLocaleString(),
    layerId: finalLayerId,
    polyline: null
  }
  
  try {
    const lngLatPoints = points.map(p => new window.T.LngLat(p.lng, p.lat))
    const polyline = new window.T.Polyline(lngLatPoints, {
      color: polylineData.color,
      weight: polylineData.weight,
      opacity: 1,
      lineStyle: polylineData.lineStyle
    })
    
    polyline.addEventListener('click', (event: any) => {
      emit('polylineClick', polylineData)
      showPolylineInfoCard(polylineData, event)
    })
    
    // 只有在图层可见时才添加到地图
    if (targetLayer.visible) {
      map.value.addOverLay(polyline)
    }
    
    polylineData.polyline = polyline
    
    // 存储标线数据到全局数组和对应图层
    customPolylines.value.push(polylineData)
    targetLayer.polylines.push(polylineData)
    
    emit('polylineAdd', polylineData)
    return polylineData
  } catch (error) {
    console.error('添加标线失败:', error)
    return null
  }
}

// ==================== 轨迹动画功能 ====================

// 检查轨迹动画依赖是否完整加载
const checkTrackAnimationDependencies = (): boolean => {
  const win = window as any

  if (!win.T) {
    console.error('天地图API未加载')
    return false
  }

  if (!win.d3) {
    console.error('D3.js库未加载，CarTrack功能需要D3.js支持')
    return false
  }

  if (!win.T.D3Overlay) {
    console.error('天地图D3支持库未加载，CarTrack功能需要D3SvgOverlay.js')
    return false
  }

  if (!win.T.CarTrack) {
    console.error('CarTrack类未加载，请确保已加载CarTrack.js')
    return false
  }

  return true
}

// 确保轨迹动画依赖已加载（按需加载）
const ensureTrackAnimationDependencies = async (): Promise<boolean> => {
  try {
    // 如果基础API未加载，先加载基础API
    if (!window.T) {
      await loadTiandituAPI()
    }

    // 加载轨迹动画相关依赖
    await loadTrackAnimationDependencies()

    // 最终检查是否全部加载完成
    return checkTrackAnimationDependencies()

  } catch (error) {
    console.error('加载轨迹动画依赖失败:', error)
    return false
  }
}

// 轨迹动画控制器接口（基于官方 CarTrack）
interface CarTrackController {
  start(): void
  pause(): void
  stop(): void
  clear(): void
  setSpeed?(speed: number): void
  isPlaying?(): boolean
  isPaused?(): boolean
  // 内部状态管理
  _isPlaying?: boolean
  _isPaused?: boolean
  _currentIndex?: number
}

// 轨迹动画数据接口
interface CarTrackData {
  id: string
  title: string
  points: Array<{lng: number, lat: number}>
  carTrack: any // T.CarTrack 实例
  controller: CarTrackController
  createTime: string
  layerId: string
  options: {
    interval?: number
    speed?: number
    dynamicLine?: boolean
    carstyle?: {
      display?: boolean
      iconUrl?: string
      width?: number
      height?: number
    }
    polylinestyle?: {
      display?: boolean
      color?: string
      width?: number
      opacity?: number
    }
    passOneNode?: (lnglat: any, index: number, length: number) => void
  }
}



// 创建轨迹动画（使用官方 CarTrack）
const createTrackAnimation = async (options: {
  points: Array<any>
  carStyle?: {
    display?: boolean
    iconUrl?: string
    width?: number
    height?: number
  }
  polylineStyle?: {
    display?: boolean
    color?: string
    width?: number
    opacity?: number
  }
  interval?: number // 时间间隔（毫秒）
  speed?: number // 移动速度（米/间隔）
  dynamicLine?: boolean // 动态轨迹线
  title?: string
  layerId?: string
}): Promise<string | null> => {
  if (!map.value || !options.points || options.points.length < 2) {
    console.error('创建轨迹动画失败：地图未初始化或轨迹点不足')
    return null
  }

  // 确保轨迹动画依赖已加载
  const dependenciesReady = await ensureTrackAnimationDependencies()
  if (!dependenciesReady) {
    console.error('轨迹动画依赖加载失败')
    return null
  }
  
  // 获取目标图层
  const finalLayerId = options.layerId || currentMarkerLayerId.value
  const targetLayer = markerLayers.value.find(layer => layer.id === finalLayerId)
  
  if (!targetLayer) {
    console.error('目标图层不存在')
    return null
  }
  
  trackAnimationCounter.value++
  const animationId = `car_track_${trackAnimationCounter.value}_${Date.now()}`
  
  try {
    // 根据速度倍数计算interval（倍数越高，interval越小，速度越快）
    const baseInterval = options.interval || 125 // 基础间隔125ms（比原来的500ms快4倍）
    const calculatedInterval = Math.max(10, baseInterval / trackAnimationSpeedMultiplier.value) // 最小间隔调整为10ms

    // 配置 CarTrack 选项
    const carTrackOptions = {
      interval: calculatedInterval,
      speed: options.speed || 10, // 默认10米/间隔
      dynamicLine: options.dynamicLine !== false, // 默认启用动态线
      Datas: options.points,
      carstyle: {
        display: options.carStyle?.display !== false,
        iconUrl: options.carStyle?.iconUrl || "http://lbs.tianditu.gov.cn/images/openlibrary/car.png",
        width: options.carStyle?.width || 52,
        height: options.carStyle?.height || 26
      },
      polylinestyle: {
        display: options.polylineStyle?.display !== false,
        color: options.polylineStyle?.color || targetLayer.color || "#2C64A7",
        width: options.polylineStyle?.width || 5,
        opacity: options.polylineStyle?.opacity || 0.9
      }
    }
    
    // 先声明控制器变量
    let controller: CarTrackController

    // 更新CarTrack配置，添加进度回调
    const carTrackOptionsWithCallback = {
      ...carTrackOptions,
      passOneNode: (lnglat: any, index: number, length: number) => {
        // 更新全局进度状态（基于官方回调）
        currentTrackProgress.value = {
          currentIndex: index,
          totalPoints: length
        }

        // 同时更新控制器的内部索引
        if (controller) {
          controller._currentIndex = index
        }
      }
    }

    // 创建 CarTrack 实例
    const carTrack = new (window as any).T.CarTrack(map.value, carTrackOptionsWithCallback)

    // 创建控制器包装器，添加状态管理
    controller = {
      _isPlaying: false,
      _isPaused: false,
      _currentIndex: 0,

      start() {
        try {
          if (carTrack && typeof carTrack.start === 'function') {
            carTrack.start()
            this._isPlaying = true
            this._isPaused = false

            console.log(`轨迹动画 ${animationId} 开始播放`)
          }
        } catch (error) {
          console.error('启动轨迹动画失败:', error)
        }
      },

      pause() {
        try {
          if (carTrack && typeof carTrack.pause === 'function') {
            carTrack.pause()
            this._isPlaying = false
            this._isPaused = true

            console.log(`轨迹动画 ${animationId} 已暂停`)
          }
        } catch (error) {
          console.error('暂停轨迹动画失败:', error)
        }
      },

      stop() {
        try {
          if (carTrack && typeof carTrack.stop === 'function') {
            carTrack.stop()
            this._isPlaying = false
            this._isPaused = false
            this._currentIndex = 0

            // 重置进度状态
            currentTrackProgress.value = { currentIndex: 0, totalPoints: currentTrackProgress.value.totalPoints }

            console.log(`轨迹动画 ${animationId} 已停止`)
          }
        } catch (error) {
          console.error('停止轨迹动画失败:', error)
        }
      },

      clear() {
        try {
          if (carTrack && typeof carTrack.clear === 'function') {
            carTrack.clear()
            this._isPlaying = false
            this._isPaused = false
            this._currentIndex = 0

            // 重置进度状态
            currentTrackProgress.value = { currentIndex: 0, totalPoints: 0 }

            console.log(`轨迹动画 ${animationId} 已清除`)
          }
        } catch (error) {
          console.error('清除轨迹动画失败:', error)
        }
      },

      setSpeed(multiplier: number) {
        try {
          // 由于CarTrack官方不支持运行时调节速度，只能在停止状态下调节
          if (this._isPlaying || this._isPaused) {
            console.warn('CarTrack不支持运行时调节速度，请先停止动画')
            return
          }

          if (carTrack && carTrack.options) {
            // 根据倍数重新计算interval
            const baseInterval = 125 // 基础间隔125ms（比原来的500ms快4倍）
            const newInterval = Math.max(10, baseInterval / multiplier) // 最小间隔调整为10ms
            carTrack.options.interval = newInterval

            console.log(`轨迹动画 ${animationId} 速度倍数已设置为: ×${multiplier} (interval: ${newInterval}ms)`)
          }
        } catch (error) {
          console.error('设置轨迹动画速度失败:', error)
        }
      },

      isPlaying() {
        try {
          // 检查CarTrack内部状态
          if (carTrack && carTrack.state !== undefined) {
            this._isPlaying = carTrack.state === 1 // 1表示播放中
            this._isPaused = carTrack.state === 3 // 3表示暂停
          }
          return this._isPlaying || false
        } catch (error) {
          console.error('获取播放状态失败:', error)
          return false
        }
      },

      isPaused() {
        try {
          // 检查CarTrack内部状态
          if (carTrack && carTrack.state !== undefined) {
            this._isPaused = carTrack.state === 3 // 3表示暂停
            this._isPlaying = carTrack.state === 1 // 1表示播放中
          }
          return this._isPaused || false
        } catch (error) {
          console.error('获取暂停状态失败:', error)
          return false
        }
      }
    }

    // 为轨迹添加点击事件监听（选择当前控制轨迹）
    if (carTrack && carTrack.polyline) {
      carTrack.polyline.addEventListener('click', () => {
        // 设置为当前控制轨迹
        currentTrackController.value = controller
        console.log(`已选择轨迹: ${options.title || animationId}`)

        // 高亮选中的轨迹
        highlightSelectedTrack(animationId)
      })
    }

    // 创建轨迹动画数据
    const trackData: CarTrackData = {
      id: animationId,
      title: options.title || `轨迹动画 ${trackAnimationCounter.value}`,
      points: [...options.points],
      carTrack: carTrack,
      controller: controller,
      createTime: new Date().toLocaleString(),
      layerId: finalLayerId,
      options: carTrackOptionsWithCallback
    }
    
    // 存储轨迹动画数据
    carTrackAnimations.value.push(trackData)
    carTrackControllers.value.set(animationId, controller)
    
    // 触发创建事件
    emit('trackAnimationCreate', trackData)
    
    console.log(`轨迹动画创建成功: ${trackData.title}, 点数: ${options.points.length}`)
    return animationId
    
  } catch (error) {
    console.error('创建轨迹动画失败:', error)
    return null
  }
}

// 移除轨迹动画
const removeTrackAnimation = (animationId: string): boolean => {
  try {
    const controller = carTrackControllers.value.get(animationId)
    if (controller) {
      // 清除动画
      controller.clear()
      
      // 从控制器映射中移除
      carTrackControllers.value.delete(animationId)
      
      // 从动画数据数组中移除
      const index = carTrackAnimations.value.findIndex(anim => anim.id === animationId)
      if (index !== -1) {
        carTrackAnimations.value.splice(index, 1)
      }
      
      // 触发移除事件
      emit('trackAnimationRemove', animationId)
      
      console.log(`轨迹动画 ${animationId} 已移除`)
      return true
    }
    return false
  } catch (error) {
    console.error('移除轨迹动画失败:', error)
    return false
  }
}

// 获取轨迹动画数据
const getTrackAnimation = (animationId: string): CarTrackData | null => {
  return carTrackAnimations.value.find(anim => anim.id === animationId) || null
}

// 获取轨迹动画控制器
const getTrackAnimationController = (animationId: string): CarTrackController | null => {
  return carTrackControllers.value.get(animationId) || null
}



// 清除所有轨迹动画
const clearAllTrackAnimations = () => {
  try {
    // 清除所有CarTrack控制器
    carTrackControllers.value.forEach((controller, id) => {
      try {
        if (controller && typeof controller.clear === 'function') {
          controller.clear()
        }
      } catch (error) {
        console.warn(`清除轨迹动画控制器 ${id} 时出错:`, error)
      }
    })
    
    // 清空数据
    carTrackAnimations.value = []
    carTrackControllers.value.clear()
    trackAnimationCounter.value = 0
    currentTrackController.value = null

    // 重置进度状态
    currentTrackProgress.value = { currentIndex: 0, totalPoints: 0 }

    console.log('所有轨迹动画已清除')
  } catch (error) {
    console.error('清除轨迹动画时发生错误:', error)
  }
}

// 轨迹控制面板相关方法
const toggleTrackControls = () => {
  trackControlsOpen.value = !trackControlsOpen.value
}



// 播放当前动画
const playCurrentAnimation = () => {
  try {
    if (currentTrackController.value && typeof currentTrackController.value.start === 'function') {
      currentTrackController.value.start()
      console.log('开始播放轨迹动画')
    } else {
      console.warn('没有可播放的轨迹动画')
    }
  } catch (error) {
    console.error('播放轨迹动画时发生错误:', error)
  }
}

// 暂停当前动画
const pauseCurrentAnimation = () => {
  try {
    if (currentTrackController.value && typeof currentTrackController.value.pause === 'function') {
      currentTrackController.value.pause()
      console.log('暂停轨迹动画')
    } else {
      console.warn('没有可暂停的轨迹动画')
    }
  } catch (error) {
    console.error('暂停轨迹动画时发生错误:', error)
  }
}

// 停止当前动画
const stopCurrentAnimation = () => {
  try {
    if (currentTrackController.value && typeof currentTrackController.value.stop === 'function') {
      currentTrackController.value.stop()
      console.log('停止轨迹动画')
    } else {
      console.warn('没有可停止的轨迹动画')
    }
  } catch (error) {
    console.error('停止轨迹动画时发生错误:', error)
  }
}



// 更新轨迹速度
const updateTrackSpeed = () => {
  try {
    if (currentTrackController.value && typeof currentTrackController.value.setSpeed === 'function') {
      // 检查是否可以调节速度（只有在停止状态下才能调节）
      const isPlaying = typeof currentTrackController.value.isPlaying === 'function' && currentTrackController.value.isPlaying()
      const isPaused = typeof currentTrackController.value.isPaused === 'function' && currentTrackController.value.isPaused()

      if (isPlaying || isPaused) {
        ElMessage.warning('请先停止动画再调节速度倍数')
        return
      }

      currentTrackController.value.setSpeed(trackAnimationSpeedMultiplier.value)
      console.log(`轨迹动画速度倍数已更新为: ×${trackAnimationSpeedMultiplier.value}`)
    } else {
      console.warn('没有可设置速度的轨迹动画')
    }
  } catch (error) {
    console.error('更新轨迹动画速度时发生错误:', error)
  }
}

// 获取当前轨迹索引（基于官方passOneNode回调）
const getCurrentTrackIndex = () => {
  return currentTrackProgress.value.currentIndex
}

// 获取总轨迹点数（基于官方passOneNode回调）
const getTotalTrackPoints = () => {
  return currentTrackProgress.value.totalPoints
}

// 获取轨迹进度百分比（基于官方passOneNode回调）
const getTrackProgressPercentage = () => {
  const current = currentTrackProgress.value.currentIndex
  const total = currentTrackProgress.value.totalPoints

  if (total === 0) return 0
  return Math.round((current / total) * 100)
}

// 获取当前轨迹标题
const getCurrentTrackTitle = () => {
  if (!currentTrackController.value) return '无轨迹'

  // 通过控制器找到对应的轨迹数据
  const trackData = carTrackAnimations.value.find(track => track.controller === currentTrackController.value)
  return trackData ? trackData.title : '未知轨迹'
}

// 获取轨迹动画状态
const getTrackAnimationStatus = () => {
  if (!currentTrackController.value) return '无动画'

  if (typeof currentTrackController.value.isPlaying === 'function' && currentTrackController.value.isPlaying()) {
    return '播放中'
  } else if (typeof currentTrackController.value.isPaused === 'function' && currentTrackController.value.isPaused()) {
    return '已暂停'
  } else {
    return '已停止'
  }
}

// 高亮选中的轨迹
const highlightSelectedTrack = (selectedId: string) => {
  try {
    // 重置所有轨迹的样式
    carTrackAnimations.value.forEach(track => {
      if (track.carTrack && track.carTrack.polyline) {
        // 恢复默认样式
        track.carTrack.polyline.setStrokeWeight(track.options.polylinestyle?.width || 5)
        track.carTrack.polyline.setStrokeOpacity(track.options.polylinestyle?.opacity || 0.8)
      }
    })

    // 高亮选中的轨迹
    const selectedTrack = carTrackAnimations.value.find(track => track.id === selectedId)
    if (selectedTrack && selectedTrack.carTrack && selectedTrack.carTrack.polyline) {
      // 增加线宽和透明度来高亮
      selectedTrack.carTrack.polyline.setStrokeWeight((selectedTrack.options.polylinestyle?.width || 5) + 2)
      selectedTrack.carTrack.polyline.setStrokeOpacity(1.0)
    }
  } catch (error) {
    console.error('高亮轨迹失败:', error)
  }
}

// 设置当前轨迹控制器
const setCurrentTrackController = (animationId: string): boolean => {
  try {
    const controller = carTrackControllers.value.get(animationId)
    if (controller) {
      currentTrackController.value = controller
      highlightSelectedTrack(animationId)
      console.log(`当前控制器已设置为轨迹: ${animationId}`)
      return true
    } else {
      console.warn(`未找到轨迹控制器: ${animationId}`)
      return false
    }
  } catch (error) {
    console.error('设置当前轨迹控制器失败:', error)
    return false
  }
}

// ==================== 轨迹动画功能结束 ====================

// 暴露方法给父组件
defineExpose({
  getMap,
  getCenter,
  getZoom,
  setCenter,
  setZoom,
  panTo,
  setMapType,
  searchPlace,
  clearSearchMarkers,
  // 标点功能
  toggleMarkerMode,
  addMarkerAtPosition,
  removeMarker,
  clearAllMarkers,
  getMarkers: () => customMarkers.value,
  getMarkerMode: () => markerMode.value,
  // 标线功能
  togglePolylineMode,
  addPolyline,
  removePolyline,
  clearAllPolylines,
  getPolylines,
  getPolylineMode: () => polylineMode.value,
  // 信息卡片功能
  showMarkerInfoCard,
  closeMarkerInfo,
  showPolylineInfoCard,
  closePolylineInfo,
  // 图层管理功能
  toggleLayerDrawer,
  addLayer,
  removeLayer,
  clearAllLayers,
  toggleLayerVisibility,
  updateLayerOpacity,
  getLayers: () => customLayers.value,
  getLayerDrawerOpen: () => layerDrawerOpen.value,
  // 标记图层管理功能
  addMarkerLayer,
  removeMarkerLayer,
  toggleMarkerLayerVisibility,
  setCurrentMarkerLayer,
  clearAllMarkerLayers,
  getMarkerLayers: () => markerLayers.value,
  getCurrentMarkerLayerId: () => currentMarkerLayerId.value,
  // 轨迹动画功能
  createTrackAnimation,
  removeTrackAnimation,
  getTrackAnimation,
  getAllTrackAnimations: () => carTrackAnimations.value,
  getTrackAnimationController,
  clearAllTrackAnimations,
  playCurrentAnimation,
  pauseCurrentAnimation,
  stopCurrentAnimation,
  updateTrackSpeed,
  getCurrentTrackIndex,
  getTotalTrackPoints,
  getTrackProgressPercentage,
  getCurrentTrackTitle,
  getTrackAnimationStatus,
  setCurrentTrackController
})

// 生命周期
onMounted(() => {
  initMap()
  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  // 移除键盘事件监听
  document.removeEventListener('keydown', handleKeyDown)

  // 清除搜索标记
  clearSearchMarkers()

  // 清除自定义标记
  clearAllMarkers()

  // 清除所有标线
  clearAllPolylines()
  
  // 清除所有图层
  clearAllLayers()
  
  // 清除所有轨迹动画
  clearAllTrackAnimations()
  
  // 销毁地图实例
  if (map.value) {
    try {
      // 销毁地图
      map.value.destroy && map.value.destroy()
    } catch (error) {
      console.warn('地图销毁时出现错误:', error)
    }
    map.value = null
  }
  
  // 重置状态
  loading.value = false
  markerMode.value = false
  polylineMode.value = false
  showMarkerInfo.value = false
  showPolylineInfo.value = false
  currentMarkerInfo.value = null
  currentPolylineInfo.value = null
})
</script>

<style scoped>
.tianditu-map-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.tianditu-map {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
}

.map-search-container {
  position: absolute;
  top: 10px;
  left: 60px;
  z-index: 1000;
  width: 300px;
}

.map-marker-controls {
  position: absolute;
  top: 140px;
  right: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.marker-toggle-btn,
.polyline-toggle-btn,
.marker-clear-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.marker-toggle-btn:hover,
.polyline-toggle-btn:hover,
.marker-clear-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.marker-toggle-btn.el-button--primary,
.polyline-toggle-btn.el-button--primary {
  background: var(--el-color-primary);
  border-color: var(--el-color-primary);
}

.marker-clear-btn {
  background: #f56c6c;
  border-color: #f56c6c;
  color: white;
}

.marker-clear-btn:hover {
  background: #f78989;
  border-color: #f78989;
}

.map-search-input {
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 6px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.map-search-input :deep(.el-input__wrapper) {
  background: transparent;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  transition: all 0.3s;
}

.map-search-input :deep(.el-input__wrapper:hover) {
  border-color: var(--el-color-primary);
}

.map-search-input :deep(.el-input__wrapper.is-focus) {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.2);
}

.search-item {
  padding: 8px 0;
}

.search-item-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.search-item-address {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  line-height: 1.2;
}

.map-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.map-loading .el-icon {
  font-size: 24px;
  color: var(--el-color-primary);
}

.map-loading span {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

/* 标记信息卡片样式 */
.marker-info-card {
  position: absolute;
  z-index: 1002;
  width: 280px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(0, 0, 0, 0.06);
  overflow: hidden;
  backdrop-filter: blur(10px);
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.marker-info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 16px 12px 16px;
  background: linear-gradient(135deg, #3f9dfd 0%, #3f9dfd 100%);
  color: white;
}

.marker-info-title-display {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.marker-info-title-edit {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.marker-info-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: white;
  flex: 1;
}

.marker-edit-btn {
  color: rgba(255, 255, 255, 0.8) !important;
  padding: 4px !important;
  min-height: auto !important;
  transition: all 0.2s;
}

.marker-edit-btn:hover {
  color: white !important;
  background: rgba(255, 255, 255, 0.2) !important;
  border-radius: 4px;
}

.marker-info-close {
  color: white !important;
  padding: 4px !important;
  min-height: auto !important;
}

.marker-info-close:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  border-radius: 4px;
}

.marker-info-content {
  padding: 16px;
}

.marker-info-item {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
}

.marker-info-item:last-child {
  margin-bottom: 0;
}

.marker-info-label {
  font-size: 13px;
  color: #666;
  font-weight: 500;
  min-width: 60px;
  margin-right: 8px;
}

.marker-info-value {
  font-size: 13px;
  color: #333;
  flex: 1;
  word-break: break-all;
}

.marker-info-actions {
  padding: 12px 16px 16px 16px;
  display: flex;
  gap: 8px;
  border-top: 1px solid #f0f0f0;
}

.marker-info-actions .el-button {
  flex: 1;
  font-size: 12px;
  height: 32px;
}

.marker-info-actions .el-button .el-icon {
  margin-right: 4px;
}

/* 标线信息卡片样式 - 复用标记信息卡片样式 */
.polyline-info-card {
  position: absolute;
  z-index: 1002;
  width: 350px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(0, 0, 0, 0.06);
  overflow: hidden;
  backdrop-filter: blur(10px);
  animation: fadeInUp 0.3s ease-out;
}

/* 标线特有的样式控制 */
.polyline-style-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 0;
}

.polyline-style-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.polyline-style-row:last-child {
  margin-bottom: 0;
}

.polyline-style-row .marker-info-label {
  min-width: 80px;
  margin-bottom: 0;
}

.polyline-color-picker {
  width: 60px;
}

.polyline-weight-input {
  width: 80px;
}

.polyline-style-select {
  width: 120px;
}

.polyline-title-input {
  flex: 1;
}

/* 标线信息卡片头部背景色 */
.polyline-info-header {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%) !important;
}

/* 图层管理抽屉样式 */
.layer-drawer {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 300px;
  background: #ffffff;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  transform: translateX(-300px);
  transition: transform 0.3s ease;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.layer-drawer-open {
  transform: translateX(0);
}

.layer-drawer-toggle {
  position: absolute;
  right: -37.5px;
  top: 50%;
  transform: translateY(-50%);
  width: 37.5px;
  height: 60px;
  background: #409eff;
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 0 6px 6px 0;
  font-size: 9px;
  gap: 3px;
  transition: background-color 0.3s ease;
}

.layer-drawer-toggle:hover {
  background: #337ecc;
}

.layer-drawer-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.layer-drawer-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.layer-drawer-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.layer-header-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.layer-header-actions .el-button {
  flex: 1;
  min-width: 0;
}

/* 标记图层管理样式 */
.marker-layer-section,
.map-layer-section {
  margin-bottom: 16px;
}

.section-title {
  margin: 0 0 12px 0;
  padding: 8px 16px;
  background: #f0f2f5;
  color: #606266;
  font-size: 14px;
  font-weight: 500;
  border-left: 3px solid #409eff;
}

.marker-layer-list {
  max-height: 300px;
  overflow-y: auto;
}

.marker-layer-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.marker-layer-item:hover {
  background: #f8f9fa;
}

.marker-layer-item.active {
  background: #e6f7ff;
  border-left: 3px solid #409eff;
}

.marker-layer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.marker-layer-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  position: relative;
}

.marker-layer-name {
  font-weight: 500;
  color: #303133;
}

.marker-layer-name-display {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-left: 4px;
}

.layer-edit-btn {
  opacity: 0;
  transition: opacity 0.2s;
  padding: 2px !important;
  min-height: auto !important;
}

.marker-layer-info:hover .layer-edit-btn {
  opacity: 1;
}

.marker-layer-name-edit {
  display: flex;
  align-items: center;
  gap: 4px;
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  background: white;
  padding: 2px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.layer-name-input {
  flex: 1;
  min-width: 80px;
}

.marker-layer-name-edit .el-button {
  padding: 4px 6px !important;
  min-height: auto !important;
}

.marker-layer-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 0 0 0 1px #dcdfe6;
}

.marker-layer-actions {
  display: flex;
  gap: 4px;
}

.marker-layer-stats {
  display: flex;
  gap: 12px;
  margin-bottom: 4px;
}

.stat-item {
  font-size: 12px;
  color: #909399;
  background: #f0f2f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.marker-layer-description {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
  margin-top: 4px;
}

/* 轨迹动画控制面板样式 */
.track-animation-controls {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

.track-controls-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #409eff;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
}

.track-controls-toggle:hover {
  background: #337ecc;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.track-controls-panel {
  position: absolute;
  bottom: 100%;
  right: 0;
  width: 320px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  margin-bottom: 10px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
  border: 1px solid #e4e7ed;
}

.track-controls-panel.track-controls-open {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.track-controls-header {
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.track-controls-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.track-controls-content {
  padding: 16px;
}

.track-control-buttons {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  margin-bottom: 16px;
}

.track-control-buttons .el-button {
  font-size: 12px;
  padding: 6px 8px;
}

.track-speed-control {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
}

.track-speed-control label {
  font-size: 12px;
  color: #606266;
  white-space: nowrap;
  font-weight: 500;
}

.speed-value {
  font-size: 12px;
  color: #409eff;
  font-weight: 600;
  min-width: 40px;
  text-align: center;
}

.track-animation-info {
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 6px;
  padding: 12px;
}

.track-animation-info p {
  margin: 0;
  font-size: 12px;
  color: #606266;
  line-height: 1.5;
}

.track-animation-info p:not(:last-child) {
  margin-bottom: 4px;
}

.empty-marker-layers {
  text-align: center;
  padding: 24px 16px;
  color: #909399;
}

.empty-marker-layers p {
  margin: 0 0 12px 0;
  font-size: 14px;
}

.layer-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.layer-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 8px;
  padding: 12px;
  transition: all 0.3s ease;
}

.layer-item:hover {
  background: #e9ecef;
  border-color: #409eff;
}

.layer-item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.layer-item-actions {
  display: flex;
  gap: 4px;
}

.layer-item-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.layer-item-type {
  font-size: 12px;
  color: #666;
  background: #e3f2fd;
  padding: 2px 8px;
  border-radius: 4px;
  display: inline-block;
  width: fit-content;
}

.layer-item-opacity {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #666;
}

.layer-item-opacity span {
  margin-right: 8px;
}

/* 轨迹动画进度条样式 */
.progress-info {
  margin-bottom: 8px;
}

.progress-text {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.track-progress-bar {
  margin-bottom: 4px;
}

.track-progress-bar :deep(.el-progress-bar__outer) {
  background-color: #e4e7ed;
  border-radius: 3px;
}

.track-progress-bar :deep(.el-progress-bar__inner) {
  background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
  border-radius: 3px;
}

/* 速度调节提示样式 */
.speed-tip {
  font-size: 11px;
  color: #999;
  margin-left: 8px;
}

/* 当前轨迹信息样式 */
.current-track-info {
  margin-bottom: 8px;
  padding: 6px 8px;
  background: #e8f5e8;
  border-radius: 4px;
  border-left: 3px solid #67c23a;

  p {
    margin: 0;
    font-size: 12px;
    color: #529b2e;
  }
}

/* 轨迹选择提示样式 */
.track-selection-tip {
  padding: 12px;
  text-align: center;
  color: #909399;
  font-size: 12px;
  background: #f5f7fa;
  border-radius: 4px;
  border: 1px dashed #dcdfe6;

  p {
    margin: 0;
    line-height: 1.4;
  }
}

/* 添加图层对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layer-drawer {
    width: 250px;
    transform: translateX(-250px);
  }
  
  .layer-drawer-toggle {
    right: -30px;
    width: 30px;
    height: 45px;
    font-size: 7.5px;
  }
}
</style>
